import { test as base, expect, BrowserContext, Page } from '@playwright/test';

export interface BrowserInfo {
  name: string;
  engine: string;
  version?: string;
  platform: string;
  isMobile: boolean;
  supportsTouch: boolean;
}

export interface BrowserCapabilities {
  localStorage: boolean;
  sessionStorage: boolean;
  indexedDB: boolean;
  webSQL: boolean;
  cookies: boolean;
  webWorkers: boolean;
  serviceWorkers: boolean;
  pushNotifications: boolean;
  geolocation: boolean;
  mediaDevices: boolean;
  webRTC: boolean;
  websockets: boolean;
  fetch: boolean;
  es6Modules: boolean;
  customElements: boolean;
  shadowDOM: boolean;
}

export class BrowserDetector {
  static async getBrowserInfo(page: Page): Promise<BrowserInfo> {
    const browserInfo = await page.evaluate(() => {
      const ua = navigator.userAgent;
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
      const supportsTouch = 'ontouchstart' in window;
      
      let name = 'unknown';
      let engine = 'unknown';
      
      if (ua.includes('Firefox')) {
        name = 'firefox';
        engine = 'gecko';
      } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
        name = 'safari';
        engine = 'webkit';
      } else if (ua.includes('Chrome')) {
        name = 'chrome';
        engine = 'blink';
      } else if (ua.includes('Edge')) {
        name = 'edge';
        engine = 'blink';
      }
      
      return {
        name,
        engine,
        platform: navigator.platform,
        isMobile,
        supportsTouch,
        userAgent: ua
      };
    });
    
    return browserInfo;
  }
  
  static async getBrowserCapabilities(page: Page): Promise<BrowserCapabilities> {
    const capabilities = await page.evaluate(() => {
      const testCapability = (test: () => boolean): boolean => {
        try {
          return test();
        } catch {
          return false;
        }
      };
      
      return {
        localStorage: testCapability(() => typeof localStorage !== 'undefined'),
        sessionStorage: testCapability(() => typeof sessionStorage !== 'undefined'),
        indexedDB: testCapability(() => typeof indexedDB !== 'undefined'),
        webSQL: testCapability(() => typeof (window as any).openDatabase !== 'undefined'),
        cookies: testCapability(() => navigator.cookieEnabled),
        webWorkers: testCapability(() => typeof Worker !== 'undefined'),
        serviceWorkers: testCapability(() => 'serviceWorker' in navigator),
        pushNotifications: testCapability(() => 'PushManager' in window),
        geolocation: testCapability(() => 'geolocation' in navigator),
        mediaDevices: testCapability(() => 'mediaDevices' in navigator),
        webRTC: testCapability(() => 'RTCPeerConnection' in window),
        websockets: testCapability(() => 'WebSocket' in window),
        fetch: testCapability(() => typeof fetch !== 'undefined'),
        es6Modules: testCapability(() => typeof Symbol !== 'undefined'),
        customElements: testCapability(() => 'customElements' in window),
        shadowDOM: testCapability(() => 'attachShadow' in Element.prototype)
      };
    });
    
    return capabilities;
  }
}

export class StorageHelper {
  static async clearAllStorage(page: Page): Promise<void> {
    await page.evaluate(() => {
      try {
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear IndexedDB
        if (indexedDB) {
          indexedDB.databases?.().then(dbs => {
            dbs.forEach(db => {
              if (db.name) {
                indexedDB.deleteDatabase(db.name);
              }
            });
          });
        }
      } catch (error) {
        console.warn('Failed to clear some storage:', error);
      }
    });
    
    // Clear cookies
    const context = page.context();
    await context.clearCookies();
  }
  
  static async getStorageInfo(page: Page): Promise<{
    localStorage: { [key: string]: string };
    sessionStorage: { [key: string]: string };
    cookies: any[];
  }> {
    const storageData = await page.evaluate(() => {
      const localData: { [key: string]: string } = {};
      const sessionData: { [key: string]: string } = {};
      
      try {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            localData[key] = localStorage.getItem(key) || '';
          }
        }
        
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key) {
            sessionData[key] = sessionStorage.getItem(key) || '';
          }
        }
      } catch (error) {
        console.warn('Failed to read storage:', error);
      }
      
      return {
        localStorage: localData,
        sessionStorage: sessionData
      };
    });
    
    const cookies = await page.context().cookies();
    
    return {
      ...storageData,
      cookies
    };
  }
}

export class NetworkHelper {
  static async interceptRequests(page: Page, callback: (request: any) => void): Promise<void> {
    await page.route('**/*', callback);
  }
  
  static async simulateNetworkConditions(page: Page, conditions: {
    offline?: boolean;
    downloadThroughput?: number;
    uploadThroughput?: number;
    latency?: number;
  }): Promise<void> {
    const context = page.context();
    if ('setOffline' in context) {
      await (context as any).setOffline(conditions.offline || false);
    }
    
    if ('setNetworkConditions' in context) {
      await (context as any).setNetworkConditions(conditions);
    }
  }
}

export class ResponsiveHelper {
  static async testViewportSizes(page: Page, sizes: Array<{width: number, height: number}>, testFn: (size: {width: number, height: number}) => Promise<void>): Promise<void> {
    for (const size of sizes) {
      await page.setViewportSize(size);
      await page.waitForTimeout(100); // Allow time for responsive changes
      await testFn(size);
    }
  }
  
  static async testOrientation(page: Page, testFn: (orientation: 'portrait' | 'landscape') => Promise<void>): Promise<void> {
    // Test portrait
    await page.setViewportSize({ width: 375, height: 812 });
    await testFn('portrait');
    
    // Test landscape
    await page.setViewportSize({ width: 812, height: 375 });
    await testFn('landscape');
  }
}

export const browserTest = base.extend<{
  browserInfo: BrowserInfo;
  browserCapabilities: BrowserCapabilities;
  storageHelper: StorageHelper;
  networkHelper: NetworkHelper;
  responsiveHelper: ResponsiveHelper;
}>({
  browserInfo: async ({ page }, use) => {
    const info = await BrowserDetector.getBrowserInfo(page);
    await use(info);
  },
  
  browserCapabilities: async ({ page }, use) => {
    const capabilities = await BrowserDetector.getBrowserCapabilities(page);
    await use(capabilities);
  },
  
  storageHelper: async ({ page }, use) => {
    await use(new StorageHelper());
  },
  
  networkHelper: async ({ page }, use) => {
    await use(new NetworkHelper());
  },
  
  responsiveHelper: async ({ page }, use) => {
    await use(new ResponsiveHelper());
  }
});

export { expect } from '@playwright/test';
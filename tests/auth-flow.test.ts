import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.describe('Login Flow', () => {
    test('should complete full login flow', async ({ page }) => {
      // Clear cookies
      await page.context().clearCookies();

      // Start at login page
      await page.goto('/login');

      // Verify we're on login page
      await expect(page).toHaveURL('/login');
      await expect(page.locator('input[name="username"]')).toBeVisible();

      // Fill login form
      await page.fill('input[name="username"]', 'testuser1');
      await page.fill('input[name="password"]', 'Testpass@1');

      // Submit form
      await page.click('button[type="submit"]');

      // Should redirect to home page
      await expect(page).toHaveURL('/chat_center');

      // Verify authentication state
      const cookies = await page.context().cookies();
      expect(cookies.some(cookie => cookie.name === 'isLogin')).toBe(true);
      expect(cookies.some(cookie => cookie.name === 'access_token')).toBe(true);
      expect(cookies.some(cookie => cookie.name === 'refresh_token')).toBe(true);
      expect(cookies.some(cookie => cookie.name === 'user_role')).toBe(true);
      expect(cookies.some(cookie => cookie.name === 'username')).toBe(true);
    });

    test('should handle API authentication', async ({ page }) => {
      // Clear cookies
      await page.context().clearCookies();

      await page.goto('/login');
      await page.fill('input[name="username"]', 'testuser1');
      await page.fill('input[name="password"]', 'Testpass@1');
      await page.click('button[type="submit"]');

      // Should redirect after successful API call
      await expect(page).toHaveURL('/chat_center');
    });
  });

  test.describe('Protected Route Access', () => {
    test('should redirect unauthenticated users to login', async ({ page }) => {
      // Clear any existing authentication cookies
      await page.context().clearCookies();

      // Try to access protected route without authentication
      await page.goto('/');

      // Should redirect to login page
      await expect(page).toHaveURL('/login');
    });

    test('should allow authenticated users to access protected routes', async ({ page }) => {
      // This test uses the authenticated state from setup

      // Access protected route
      await page.goto('/');

      // Should redirect to chat_center (since root redirects authenticated users)
      await expect(page).toHaveURL('/chat_center');
    });

    test('should handle expired authentication', async ({ page }) => {
      // Clear existing auth and set up expired token scenario
      await page.context().clearCookies();
      await page.context().addCookies([
        {
          name: 'isLogin',
          value: 'true',
          domain: 'localhost',
          path: '/',
        },
        {
          name: 'access_token',
          value: 'expired-token',
          domain: 'localhost',
          path: '/',
        }
      ]);

      await page.context().addCookies([
        {
          name: 'isLogin',
          value: 'false',
          domain: 'localhost',
          path: '/',
        }
      ]);

      // Try to access protected route
      await page.goto('/');

      // Should redirect to login due to expired token
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('Session Management', () => {
    test('should maintain session across page refreshes', async ({ page }) => {
      // Clear cookies
      await page.context().clearCookies();

      // Login
      await page.goto('/login');
      await page.fill('input[name="username"]', 'testuser1');
      await page.fill('input[name="password"]', 'Testpass@1');
      await page.click('button[type="submit"]');
      await expect(page).toHaveURL('/chat_center');

      // Refresh page
      await page.reload();

      // Should remain authenticated
      await expect(page).toHaveURL('/chat_center');
    });

    test('should handle concurrent sessions', async ({ browser }) => {
      // Create two contexts for different sessions (they start clean by default)
      const context1 = await browser.newContext();
      const context2 = await browser.newContext();

      const page1 = await context1.newPage();
      const page2 = await context2.newPage();

      // Login in first context
      await page1.goto('/login');
      await page1.fill('input[name="username"]', 'testuser1');
      await page1.fill('input[name="password"]', 'Testpass@1');
      await page1.click('button[type="submit"]');

      // Login in second context
      await page2.goto('/login');
      await page2.fill('input[name="username"]', 'testuser2');
      await page2.fill('input[name="password"]', 'Testpass@1');
      await page2.click('button[type="submit"]');

      // Both sessions should be independent
      await expect(page1).toHaveURL('/chat_center');
      await expect(page2).toHaveURL('/chat_center');

      await context1.close();
      await context2.close();
    });
  });
});
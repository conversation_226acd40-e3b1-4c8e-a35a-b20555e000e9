export interface BrowserTestData {
  browser: string;
  userAgent: string;
  capabilities: {
    [key: string]: boolean;
  };
  expectedBehaviors: {
    [key: string]: any;
  };
  knownLimitations: string[];
  testCredentials: {
    username: string;
    password: string;
  };
}

export const browserTestData: { [key: string]: BrowserTestData } = {
  chrome: {
    browser: 'chrome',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    capabilities: {
      fetch: true,
      websockets: true,
      webworkers: true,
      serviceworkers: true,
      notifications: true,
      geolocation: true,
      mediaDevices: true,
      webrtc: true,
      localStorage: true,
      sessionStorage: true,
      indexedDB: true,
      cookies: true,
      customElements: true,
      shadowDOM: true,
      webComponents: true,
      intersectionObserver: true,
      resizeObserver: true,
      performanceObserver: true,
      webGL: true,
      webGL2: true,
      css3d: true,
      flexbox: true,
      grid: true,
      cssVariables: true,
      es6: true,
      modules: true,
      asyncAwait: true,
      promises: true,
      symbols: true,
      maps: true,
      sets: true,
      proxy: true,
      generators: true,
      classes: true,
      arrowFunctions: true,
      templateLiterals: true,
      destructuring: true,
      spreadOperator: true
    },
    expectedBehaviors: {
      passwordManagerIntegration: true,
      autofillSupport: true,
      credentialsAPI: true,
      http2Support: true,
      networkOptimizations: true,
      imageFormats: ['webp', 'avif', 'jpeg', 'png', 'gif', 'svg'],
      videoFormats: ['webm', 'mp4', 'ogg'],
      audioFormats: ['webm', 'mp4', 'ogg', 'wav'],
      compressionSupport: ['gzip', 'brotli'],
      maxCookieSize: 4096,
      maxLocalStorageSize: 10485760, // 10MB
      maxSessionStorageSize: 10485760,
      defaultViewport: { width: 1280, height: 720 },
      devicePixelRatio: 1,
      colorDepth: 24,
      touchSupport: false,
      maxTouchPoints: 0
    },
    knownLimitations: [
      'Autoplay restrictions for audio/video',
      'Third-party cookie restrictions in incognito mode',
      'WebRTC IP leakage potential',
      'Memory usage for large datasets'
    ],
    testCredentials: {
      username: '<EMAIL>',
      password: 'ChromeTest123!'
    }
  },

  firefox: {
    browser: 'firefox',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
    capabilities: {
      fetch: true,
      websockets: true,
      webworkers: true,
      serviceworkers: true,
      notifications: true,
      geolocation: true,
      mediaDevices: true,
      webrtc: true,
      localStorage: true,
      sessionStorage: true,
      indexedDB: true,
      cookies: true,
      customElements: true,
      shadowDOM: true,
      webComponents: true,
      intersectionObserver: true,
      resizeObserver: true,
      performanceObserver: false, // Limited support
      webGL: true,
      webGL2: true,
      css3d: true,
      flexbox: true,
      grid: true,
      cssVariables: true,
      es6: true,
      modules: true,
      asyncAwait: true,
      promises: true,
      symbols: true,
      maps: true,
      sets: true,
      proxy: true,
      generators: true,
      classes: true,
      arrowFunctions: true,
      templateLiterals: true,
      destructuring: true,
      spreadOperator: true
    },
    expectedBehaviors: {
      passwordManagerIntegration: true,
      autofillSupport: true,
      credentialsAPI: false, // Limited support
      http2Support: true,
      networkOptimizations: true,
      imageFormats: ['webp', 'jpeg', 'png', 'gif', 'svg'], // No AVIF yet
      videoFormats: ['webm', 'mp4', 'ogg'],
      audioFormats: ['webm', 'mp4', 'ogg', 'wav'],
      compressionSupport: ['gzip', 'brotli'],
      maxCookieSize: 4096,
      maxLocalStorageSize: 10485760,
      maxSessionStorageSize: 10485760,
      defaultViewport: { width: 1280, height: 720 },
      devicePixelRatio: 1,
      colorDepth: 24,
      touchSupport: false,
      maxTouchPoints: 0,
      strictCSP: true,
      trackingProtection: true
    },
    knownLimitations: [
      'Limited Performance Observer support',
      'No AVIF image format support',
      'Stricter content security policies',
      'Mixed content blocking by default'
    ],
    testCredentials: {
      username: '<EMAIL>',
      password: 'FirefoxTest123!'
    }
  },

  safari: {
    browser: 'safari',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    capabilities: {
      fetch: true,
      websockets: true,
      webworkers: true,
      serviceworkers: true,
      notifications: true,
      geolocation: true,
      mediaDevices: true,
      webrtc: true,
      localStorage: true,
      sessionStorage: true,
      indexedDB: true,
      cookies: true,
      customElements: true,
      shadowDOM: true,
      webComponents: true,
      intersectionObserver: true,
      resizeObserver: true,
      performanceObserver: false,
      webGL: true,
      webGL2: true,
      css3d: true,
      flexbox: true,
      grid: true,
      cssVariables: true,
      es6: true,
      modules: true,
      asyncAwait: true,
      promises: true,
      symbols: true,
      maps: true,
      sets: true,
      proxy: true,
      generators: true,
      classes: true,
      arrowFunctions: true,
      templateLiterals: true,
      destructuring: true,
      spreadOperator: true
    },
    expectedBehaviors: {
      passwordManagerIntegration: true,
      autofillSupport: true,
      credentialsAPI: false,
      http2Support: true,
      networkOptimizations: true,
      imageFormats: ['webp', 'jpeg', 'png', 'gif', 'svg', 'heic'], // HEIC support on macOS
      videoFormats: ['mp4', 'mov', 'webm'],
      audioFormats: ['mp4', 'mp3', 'wav', 'aac'],
      compressionSupport: ['gzip', 'brotli'],
      maxCookieSize: 4096,
      maxLocalStorageSize: 5242880, // 5MB (stricter)
      maxSessionStorageSize: 5242880,
      defaultViewport: { width: 1280, height: 720 },
      devicePixelRatio: 2, // Retina displays
      colorDepth: 24,
      touchSupport: false, // Desktop Safari
      maxTouchPoints: 0,
      strictCORS: true,
      intelligentTrackingPrevention: true,
      privateStorageMode: true
    },
    knownLimitations: [
      'Intelligent Tracking Prevention affects cookies',
      'Stricter localStorage quotas in private mode',
      'Limited Performance Observer support',
      'WebRTC restrictions for privacy',
      'Autoplay restrictions'
    ],
    testCredentials: {
      username: '<EMAIL>',
      password: 'SafariTest123!'
    }
  },

  edge: {
    browser: 'edge',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    capabilities: {
      fetch: true,
      websockets: true,
      webworkers: true,
      serviceworkers: true,
      notifications: true,
      geolocation: true,
      mediaDevices: true,
      webrtc: true,
      localStorage: true,
      sessionStorage: true,
      indexedDB: true,
      cookies: true,
      customElements: true,
      shadowDOM: true,
      webComponents: true,
      intersectionObserver: true,
      resizeObserver: true,
      performanceObserver: true,
      webGL: true,
      webGL2: true,
      css3d: true,
      flexbox: true,
      grid: true,
      cssVariables: true,
      es6: true,
      modules: true,
      asyncAwait: true,
      promises: true,
      symbols: true,
      maps: true,
      sets: true,
      proxy: true,
      generators: true,
      classes: true,
      arrowFunctions: true,
      templateLiterals: true,
      destructuring: true,
      spreadOperator: true
    },
    expectedBehaviors: {
      passwordManagerIntegration: true,
      autofillSupport: true,
      credentialsAPI: true,
      http2Support: true,
      networkOptimizations: true,
      imageFormats: ['webp', 'avif', 'jpeg', 'png', 'gif', 'svg'],
      videoFormats: ['webm', 'mp4', 'ogg'],
      audioFormats: ['webm', 'mp4', 'ogg', 'wav'],
      compressionSupport: ['gzip', 'brotli'],
      maxCookieSize: 4096,
      maxLocalStorageSize: 10485760,
      maxSessionStorageSize: 10485760,
      defaultViewport: { width: 1280, height: 720 },
      devicePixelRatio: 1,
      colorDepth: 24,
      touchSupport: false,
      maxTouchPoints: 0,
      microsoftIntegration: true,
      enhancedSecurity: true
    },
    knownLimitations: [
      'Microsoft-specific integrations may affect testing',
      'Enhanced security features may block some content',
      'Different update cycle than Chrome'
    ],
    testCredentials: {
      username: '<EMAIL>',
      password: 'EdgeTest123!'
    }
  }
};


export const testScenarios = {
  crossBrowser: {
    login: {
      validCredentials: {
        username: '<EMAIL>',
        password: 'ValidPassword123!'
      },
      invalidCredentials: {
        username: '<EMAIL>',
        password: 'WrongPassword'
      },
      emptyCredentials: {
        username: '',
        password: ''
      },
      securityTests: {
        sqlInjection: {
          username: "admin'; DROP TABLE users; --",
          password: 'password'
        },
        xssAttack: {
          username: '<script>alert("xss")</script>',
          password: 'password'
        },
        pathTraversal: {
          username: '../../../etc/passwd',
          password: 'password'
        }
      }
    },
    storage: {
      testData: {
        simple: { key: 'test-key', value: 'test-value' },
        complex: { key: 'complex-data', value: { id: 1, name: 'test', array: [1, 2, 3] } },
        large: { key: 'large-data', value: 'x'.repeat(1024 * 1024) }, // 1MB
        unicode: { key: 'unicode-test', value: '🎉 Test émojis ánd spëcial çharacters' }
      },
      quotaTests: {
        localStorage: 10485760, // 10MB
        sessionStorage: 10485760,
        indexedDB: 1073741824 // 1GB (theoretical)
      }
    },
    network: {
      endpoints: {
        success: '/api/success',
        notFound: '/api/404',
        serverError: '/api/500',
        timeout: '/api/timeout',
        cors: 'https://httpbin.org/get',
        https: 'https://httpbin.org/status/200',
        websocket: 'ws://localhost:8080/test'
      },
      headers: {
        standard: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        custom: {
          'X-Custom-Header': 'custom-value',
          'X-Test-ID': '12345'
        },
        cors: {
          'X-Requested-With': 'XMLHttpRequest',
          'Origin': 'http://localhost:8000'
        }
      }
    }
  }
};

export const browserQuirks = {
  chrome: {
    autofill: 'Uses Google account integration',
    performance: 'V8 engine optimizations',
    security: 'Site isolation and sandboxing',
    limitations: ['Memory usage', 'Battery impact']
  },
  firefox: {
    autofill: 'Uses Firefox Sync',
    performance: 'SpiderMonkey engine',
    security: 'Enhanced tracking protection',
    limitations: ['Slower JavaScript in some cases', 'Different image format support']
  },
  safari: {
    autofill: 'Uses iCloud Keychain',
    performance: 'JavaScriptCore engine',
    security: 'Intelligent tracking prevention',
    limitations: ['Webkit-specific behavior', 'Limited storage in private mode']
  },
  edge: {
    autofill: 'Uses Microsoft account',
    performance: 'Chromium-based V8 engine',
    security: 'Microsoft Defender integration',
    limitations: ['Microsoft-specific features', 'Different update cycle']
  }
};

export const accessibilityTestData = {
  screenReaderTexts: {
    loginForm: 'Login form',
    usernameField: 'Username or email address',
    passwordField: 'Password',
    submitButton: 'Sign in',
    errorMessage: 'Invalid credentials',
    successMessage: 'Login successful'
  },
  ariaLabels: {
    form: 'User authentication form',
    username: 'Enter your username or email',
    password: 'Enter your password',
    submit: 'Submit login form',
    loading: 'Signing in, please wait',
    error: 'Login error message'
  },
  keyboardNavigation: {
    tabOrder: ['username', 'password', 'submit', 'forgot-password'],
    shortcuts: {
      submit: 'Enter',
      cancel: 'Escape',
      clear: 'Ctrl+Shift+Delete'
    }
  }
};

export function getBrowserTestData(browserName: string): BrowserTestData {
  const data = browserTestData[browserName];
  if (!data) {
    throw new Error(`No test data available for browser: ${browserName}`);
  }
  return data;
}

export function isMobileBrowser(browserName: string): boolean {
  return false; // Mobile tests removed
}

export function getBrowserCapabilities(browserName: string): { [key: string]: boolean } {
  const data = getBrowserTestData(browserName);
  return data.capabilities;
}

export function getBrowserLimitations(browserName: string): string[] {
  const data = getBrowserTestData(browserName);
  return data.knownLimitations;
}

export function getTestCredentials(browserName: string): { username: string; password: string } {
  const data = getBrowserTestData(browserName);
  return data.testCredentials;
}
import { browserTest as test, expect, BrowserInfo } from '../utils/browser-config';

test.describe('Browser-Specific Login Behavior', () => {
  let browserInfo: BrowserInfo;

  test.beforeEach(async ({ page, browserInfo: info }) => {
    browserInfo = info;
    await page.goto('/login');
  });

  test.describe('Browser Autofill and Password Management', () => {
    test('should handle browser password manager integration', async ({ page }) => {
      const loginForm = await page.locator('form').first();
      const usernameInput = await page.locator('input[type="text"], input[type="email"]').first();
      const passwordInput = await page.locator('input[type="password"]').first();

      await expect(usernameInput).toBeVisible();
      await expect(passwordInput).toBeVisible();

      // Check for autocomplete attributes
      const autocompleteResult = await page.evaluate(() => {
        const usernameField = document.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        const passwordField = document.querySelector('input[type="password"]') as HTMLInputElement;
        
        return {
          usernameAutocomplete: usernameField?.autocomplete || usernameField?.getAttribute('autocomplete'),
          passwordAutocomplete: passwordField?.autocomplete || passwordField?.getAttribute('autocomplete'),
          formAutocomplete: (document.querySelector('form') as HTMLFormElement)?.autocomplete || 'on'
        };
      });

      expect(['username', 'email', 'on', '']).toContain(autocompleteResult.usernameAutocomplete);
      expect(['current-password', 'password', 'on', '']).toContain(autocompleteResult.passwordAutocomplete);
    });

    test('should handle browser-specific autofill behavior', async ({ page }) => {
      const testCredentials = {
        username: '<EMAIL>',
        password: 'testpassword123'
      };

      await page.fill('input[type="text"], input[type="email"]', testCredentials.username);
      await page.fill('input[type="password"]', testCredentials.password);

      // Check if browser detects login form
      const loginFormDetection = await page.evaluate(() => {
        const form = document.querySelector('form') as HTMLFormElement;
        const inputs = form?.querySelectorAll('input');
        
        let hasUsernameField = false;
        let hasPasswordField = false;
        
        inputs?.forEach(input => {
          if (input.type === 'text' || input.type === 'email') {
            hasUsernameField = true;
          }
          if (input.type === 'password') {
            hasPasswordField = true;
          }
        });
        
        return {
          hasUsernameField,
          hasPasswordField,
          formMethod: form?.method || 'get',
          formAction: form?.action || '',
          inputCount: inputs?.length || 0
        };
      });

      expect(loginFormDetection.hasUsernameField).toBe(true);
      expect(loginFormDetection.hasPasswordField).toBe(true);
      expect(loginFormDetection.inputCount).toBeGreaterThan(1);
    });
  });

  test.describe('Browser Security Features', () => {
    test('should handle HTTPS requirements for secure login', async ({ page }) => {
      const securityFeatures = await page.evaluate(() => {
        return {
          isSecureContext: window.isSecureContext,
          protocol: window.location.protocol,
          hasPasswordField: !!document.querySelector('input[type="password"]'),
          mixedContent: window.location.protocol === 'https:' && 
                       document.querySelector('script[src^="http:"], link[href^="http:"]') !== null
        };
      });

      if (securityFeatures.protocol === 'https:') {
        expect(securityFeatures.isSecureContext).toBe(true);
        expect(securityFeatures.mixedContent).toBe(false);
      }
    });

    test('should handle Content Security Policy', async ({ page }) => {
      const cspResult = await page.evaluate(() => {
        const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        return {
          hasCSPMeta: !!metaCSP,
          cspContent: metaCSP?.getAttribute('content') || '',
          reportURI: metaCSP?.getAttribute('content')?.includes('report-uri') || false
        };
      });

      // CSP is optional but if present, should be properly configured
      if (cspResult.hasCSPMeta) {
        expect(cspResult.cspContent).toBeTruthy();
      }
    });

    test('should handle password field security', async ({ page }) => {
      const passwordSecurity = await page.evaluate(() => {
        const passwordField = document.querySelector('input[type="password"]') as HTMLInputElement;
        
        if (!passwordField) return { hasPasswordField: false };
        
        return {
          hasPasswordField: true,
          autocompleteValue: passwordField.autocomplete || passwordField.getAttribute('autocomplete'),
          hasName: !!passwordField.name,
          hasId: !!passwordField.id,
          isRequired: passwordField.required,
          minLength: passwordField.minLength,
          maxLength: passwordField.maxLength,
          pattern: passwordField.pattern,
          spellcheck: passwordField.spellcheck
        };
      });

      if (passwordSecurity.hasPasswordField) {
        expect(passwordSecurity.spellcheck).toBe(false);
        expect(passwordSecurity.hasName || passwordSecurity.hasId).toBe(true);
      }
    });
  });

  test.describe('Safari-Specific Login Behavior', () => {
    test('should handle Safari password autofill', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari', 'Safari-specific test');

      const safariAutofill = await page.evaluate(() => {
        const passwordField = document.querySelector('input[type="password"]') as HTMLInputElement;
        const usernameField = document.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        
        return {
          passwordFieldAutocomplete: passwordField?.autocomplete,
          usernameFieldAutocomplete: usernameField?.autocomplete,
          supportsPasswordRules: 'passwordRules' in (passwordField || {}),
          hasTextContentType: usernameField?.getAttribute('textContentType') !== null
        };
      });

      expect(safariAutofill.passwordFieldAutocomplete).toBeDefined();
      expect(safariAutofill.usernameFieldAutocomplete).toBeDefined();
    });

    test('should handle Safari touch ID integration', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari' || !browserInfo.isMobile, 'Safari mobile specific test');

      const touchIdSupport = await page.evaluate(() => {
        return {
          hasWebAuthn: 'credentials' in navigator,
          hasPublicKeyCredential: 'PublicKeyCredential' in window,
          platform: navigator.platform,
          touchIdAvailable: 'authenticate' in (navigator as any) || 'credentials' in navigator
        };
      });

      expect(touchIdSupport.hasWebAuthn || touchIdSupport.hasPublicKeyCredential).toBe(true);
    });
  });

  test.describe('Chrome-Specific Login Behavior', () => {
    test('should handle Chrome password manager', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');

      const chromePasswordManager = await page.evaluate(() => {
        const passwordField = document.querySelector('input[type="password"]') as HTMLInputElement;
        const usernameField = document.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        
        return {
          passwordFieldAutocomplete: passwordField?.autocomplete,
          usernameFieldAutocomplete: usernameField?.autocomplete,
          hasCredentialsAPI: 'credentials' in navigator,
          hasPasswordCredential: 'PasswordCredential' in window,
          chromeSpecific: 'chrome' in window
        };
      });

      expect(chromePasswordManager.hasCredentialsAPI).toBe(true);
      expect(chromePasswordManager.chromeSpecific).toBe(true);
    });

    test('should handle Chrome autofill suggestions', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');

      const usernameField = await page.locator('input[type="text"], input[type="email"]').first();
      const passwordField = await page.locator('input[type="password"]').first();

      await usernameField.click();
      await page.keyboard.type('test');

      const autofillBehavior = await page.evaluate(() => {
        const usernameInput = document.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        const passwordInput = document.querySelector('input[type="password"]') as HTMLInputElement;
        
        return {
          usernameValue: usernameInput?.value,
          passwordValue: passwordInput?.value,
          activeElement: document.activeElement?.tagName,
          hasAutofillStyles: window.getComputedStyle(usernameInput).backgroundColor !== 'rgba(0, 0, 0, 0)'
        };
      });

      expect(autofillBehavior.usernameValue).toContain('test');
      expect(autofillBehavior.activeElement).toBe('INPUT');
    });
  });

  test.describe('Firefox-Specific Login Behavior', () => {
    test('should handle Firefox password manager', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');

      const firefoxPasswordManager = await page.evaluate(() => {
        const passwordField = document.querySelector('input[type="password"]') as HTMLInputElement;
        const usernameField = document.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        
        return {
          passwordFieldAutocomplete: passwordField?.autocomplete,
          usernameFieldAutocomplete: usernameField?.autocomplete,
          mozSpecific: 'mozInnerScreenX' in window,
          hasFirefoxAPI: navigator.userAgent.includes('Firefox')
        };
      });

      expect(firefoxPasswordManager.hasFirefoxAPI).toBe(true);
    });

    test('should handle Firefox security warnings', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');

      const securityWarnings = await page.evaluate(() => {
        return {
          isSecureContext: window.isSecureContext,
          hasPasswordField: !!document.querySelector('input[type="password"]'),
          protocol: window.location.protocol,
          mixedContentBlocked: false // Firefox blocks mixed content by default
        };
      });

      if (securityWarnings.protocol === 'http:' && securityWarnings.hasPasswordField) {
        expect(securityWarnings.isSecureContext).toBe(false);
      }
    });
  });

  test.describe('Cross-Browser Form Submission', () => {
    test('should handle form submission consistently', async ({ page }) => {
      const testCredentials = {
        username: '<EMAIL>',
        password: 'testpassword123'
      };

      const formSubmission = await page.evaluate(() => {
        const form = document.querySelector('form') as HTMLFormElement;
        const submitButton = form?.querySelector('button[type="submit"], input[type="submit"]') as HTMLButtonElement;
        
        return {
          formMethod: form?.method || 'get',
          formAction: form?.action || '',
          hasSubmitButton: !!submitButton,
          buttonType: submitButton?.type || 'button',
          formEnctype: form?.enctype || 'application/x-www-form-urlencoded',
          formTarget: form?.target || '_self'
        };
      });

      expect(formSubmission.formMethod.toLowerCase()).toBe('post');
      expect(formSubmission.hasSubmitButton).toBe(true);
      expect(formSubmission.formEnctype).toBe('application/x-www-form-urlencoded');
    });

    test('should handle form validation before submission', async ({ page }) => {
      const usernameField = await page.locator('input[type="text"], input[type="email"]').first();
      const passwordField = await page.locator('input[type="password"]').first();

      // Test empty form validation
      const emptyValidation = await page.evaluate(() => {
        const form = document.querySelector('form') as HTMLFormElement;
        const usernameInput = form?.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        const passwordInput = form?.querySelector('input[type="password"]') as HTMLInputElement;
        
        return {
          formValid: form?.checkValidity(),
          usernameValid: usernameInput?.checkValidity(),
          passwordValid: passwordInput?.checkValidity(),
          usernameRequired: usernameInput?.required,
          passwordRequired: passwordInput?.required
        };
      });

      if (emptyValidation.usernameRequired) {
        expect(emptyValidation.usernameValid).toBe(false);
      }
      if (emptyValidation.passwordRequired) {
        expect(emptyValidation.passwordValid).toBe(false);
      }

      // Fill form and test validation
      await usernameField.fill('<EMAIL>');
      await passwordField.fill('testpassword123');

      const filledValidation = await page.evaluate(() => {
        const form = document.querySelector('form') as HTMLFormElement;
        const usernameInput = form?.querySelector('input[type="text"], input[type="email"]') as HTMLInputElement;
        const passwordInput = form?.querySelector('input[type="password"]') as HTMLInputElement;
        
        return {
          formValid: form?.checkValidity(),
          usernameValid: usernameInput?.checkValidity(),
          passwordValid: passwordInput?.checkValidity(),
          usernameValue: usernameInput?.value,
          passwordValue: passwordInput?.value
        };
      });

      expect(filledValidation.usernameValid).toBe(true);
      expect(filledValidation.passwordValid).toBe(true);
      expect(filledValidation.usernameValue).toBe('testuserexample.com');
      expect(filledValidation.passwordValue).toBe('testpassword123');
    });
  });

  test.describe('Browser Session Management', () => {
    test('should handle remember me functionality', async ({ page }) => {
      const rememberMeCheckbox = page.locator('input[type="checkbox"]').first();
      
      if (await rememberMeCheckbox.isVisible()) {
        await rememberMeCheckbox.check();
        
        const checkboxState = await page.evaluate(() => {
          const checkbox = document.querySelector('input[type="checkbox"]') as HTMLInputElement;
          return {
            checked: checkbox?.checked,
            value: checkbox?.value,
            name: checkbox?.name
          };
        });

        expect(checkboxState.checked).toBe(true);
      }
    });

    test('should handle session storage for login state', async ({ page }) => {
      const sessionManagement = await page.evaluate(() => {
        try {
          sessionStorage.setItem('login-test', 'test-value');
          const retrieved = sessionStorage.getItem('login-test');
          sessionStorage.removeItem('login-test');
          
          return {
            supportsSessionStorage: true,
            canSetItem: retrieved === 'test-value',
            error: null
          };
        } catch (error) {
          return {
            supportsSessionStorage: false,
            canSetItem: false,
            error: error.message
          };
        }
      });

      expect(sessionManagement.supportsSessionStorage).toBe(true);
      expect(sessionManagement.canSetItem).toBe(true);
    });

    test('should handle local storage for persistent login', async ({ page }) => {
      const persistentLogin = await page.evaluate(() => {
        try {
          localStorage.setItem('persistent-login-test', 'test-value');
          const retrieved = localStorage.getItem('persistent-login-test');
          localStorage.removeItem('persistent-login-test');
          
          return {
            supportsLocalStorage: true,
            canPersist: retrieved === 'test-value',
            error: null
          };
        } catch (error) {
          return {
            supportsLocalStorage: false,
            canPersist: false,
            error: error.message
          };
        }
      });

      expect(persistentLogin.supportsLocalStorage).toBe(true);
      expect(persistentLogin.canPersist).toBe(true);
    });
  });

  test.describe('Login Error Handling', () => {
    test('should handle network errors consistently', async ({ page }) => {
      const networkErrorHandling = await page.evaluate(() => {
        return {
          hasFetch: typeof fetch !== 'undefined',
          hasXHR: typeof XMLHttpRequest !== 'undefined',
          supportsPromises: typeof Promise !== 'undefined',
          supportsAsyncAwait: (async () => {
            try {
              await Promise.resolve();
              return true;
            } catch {
              return false;
            }
          })()
        };
      });

      expect(networkErrorHandling.hasFetch).toBe(true);
      expect(networkErrorHandling.hasXHR).toBe(true);
      expect(networkErrorHandling.supportsPromises).toBe(true);
    });

    test('should display error messages appropriately', async ({ page }) => {
      const errorDisplay = await page.evaluate(() => {
        const errorElements = document.querySelectorAll('.error, .alert, .warning, [role="alert"]');
        const form = document.querySelector('form');
        
        return {
          hasErrorElements: errorElements.length > 0,
          errorElementCount: errorElements.length,
          hasFormValidation: form && 'checkValidity' in form,
          hasAriaLive: document.querySelector('[aria-live]') !== null,
          hasRoleAlert: document.querySelector('[role="alert"]') !== null
        };
      });

      expect(errorDisplay.hasFormValidation).toBe(true);
    });
  });
});
import { browserTest as test, expect, BrowserInfo, BrowserCapabilities } from '../utils/browser-config';

test.describe('Browser Network Compatibility', () => {
  let browserInfo: BrowserInfo;
  let browserCapabilities: BrowserCapabilities;

  test.beforeEach(async ({ page, browserInfo: info, browserCapabilities: caps }) => {
    browserInfo = info;
    browserCapabilities = caps;
    await page.goto('/');
  });

  test.describe('Fetch API vs XMLHttpRequest', () => {
    test('should support Fetch API', async ({ page }) => {
      test.skip(!browserCapabilities.fetch, 'Fetch API not supported in this browser');
      
      const fetchResult = await page.evaluate(async () => {
        try {
          const response = await fetch('/api/test', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          return {
            success: true,
            status: response.status,
            supportsHeaders: 'headers' in response,
            supportsJson: 'json' in response,
            supportsText: 'text' in response
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            supportsHeaders: false,
            supportsJson: false,
            supportsText: false
          };
        }
      });

      expect(fetchResult.supportsHeaders).toBe(true);
      expect(fetchResult.supportsJson).toBe(true);
      expect(fetchResult.supportsText).toBe(true);
    });

    test('should support XMLHttpRequest as fallback', async ({ page }) => {
      const xhrResult = await page.evaluate(() => {
        return new Promise((resolve) => {
          try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/test', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onreadystatechange = function() {
              if (xhr.readyState === 4) {
                resolve({
                  success: true,
                  status: xhr.status,
                  supportsHeaders: 'setRequestHeader' in xhr,
                  supportsResponseType: 'responseType' in xhr,
                  supportsTimeout: 'timeout' in xhr
                });
              }
            };
            
            xhr.onerror = function() {
              resolve({
                success: false,
                error: 'Request failed',
                supportsHeaders: 'setRequestHeader' in xhr,
                supportsResponseType: 'responseType' in xhr,
                supportsTimeout: 'timeout' in xhr
              });
            };
            
            xhr.send();
          } catch (error) {
            resolve({
              success: false,
              error: error.message,
              supportsHeaders: false,
              supportsResponseType: false,
              supportsTimeout: false
            });
          }
        });
      });

      expect(xhrResult.supportsHeaders).toBe(true);
      expect(xhrResult.supportsResponseType).toBe(true);
      expect(xhrResult.supportsTimeout).toBe(true);
    });

    test('should handle request headers consistently', async ({ page }) => {
      const headerResult = await page.evaluate(async () => {
        const customHeaders = {
          'X-Custom-Header': 'custom-value',
          'User-Agent': 'test-agent',
          'Accept': 'application/json'
        };
        
        const results = { fetch: null, xhr: null };
        
        // Test with Fetch API
        if (typeof fetch !== 'undefined') {
          try {
            const response = await fetch('/api/test', {
              method: 'GET',
              headers: customHeaders
            });
            results.fetch = {
              success: true,
              headersSupported: true
            };
          } catch (error) {
            results.fetch = {
              success: false,
              error: error.message,
              headersSupported: false
            };
          }
        }
        
        // Test with XMLHttpRequest
        await new Promise((resolve) => {
          try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/test', true);
            
            Object.entries(customHeaders).forEach(([key, value]) => {
              xhr.setRequestHeader(key, value);
            });
            
            xhr.onreadystatechange = function() {
              if (xhr.readyState === 4) {
                results.xhr = {
                  success: true,
                  headersSupported: true
                };
                resolve(null);
              }
            };
            
            xhr.onerror = function() {
              results.xhr = {
                success: false,
                error: 'XHR failed',
                headersSupported: true
              };
              resolve(null);
            };
            
            xhr.send();
          } catch (error) {
            results.xhr = {
              success: false,
              error: error.message,
              headersSupported: false
            };
            resolve(null);
          }
        });
        
        return results;
      });

      if (headerResult.fetch) {
        expect(headerResult.fetch.headersSupported).toBe(true);
      }
      if (headerResult.xhr) {
        expect(headerResult.xhr.headersSupported).toBe(true);
      }
    });
  });

  test.describe('WebSocket Compatibility', () => {
    test('should support WebSocket connections', async ({ page }) => {
      test.skip(!browserCapabilities.websockets, 'WebSocket not supported in this browser');
      
      const wsResult = await page.evaluate(() => {
        return new Promise((resolve) => {
          try {
            const ws = new WebSocket('ws://localhost:8080/test');
            
            const timeout = setTimeout(() => {
              ws.close();
              resolve({
                success: false,
                error: 'Connection timeout',
                supportsWebSocket: true
              });
            }, 1000);
            
            ws.onopen = function() {
              clearTimeout(timeout);
              ws.close();
              resolve({
                success: true,
                supportsWebSocket: true
              });
            };
            
            ws.onerror = function() {
              clearTimeout(timeout);
              resolve({
                success: false,
                error: 'Connection failed',
                supportsWebSocket: true
              });
            };
            
          } catch (error) {
            resolve({
              success: false,
              error: error.message,
              supportsWebSocket: false
            });
          }
        });
      });

      expect(wsResult.supportsWebSocket).toBe(true);
    });

    test('should handle WebSocket message formats', async ({ page }) => {
      test.skip(!browserCapabilities.websockets, 'WebSocket not supported in this browser');
      
      const messageResult = await page.evaluate(() => {
        return new Promise((resolve) => {
          try {
            const ws = new WebSocket('ws://localhost:8080/echo');
            
            const timeout = setTimeout(() => {
              ws.close();
              resolve({
                success: false,
                error: 'Message timeout',
                supportsText: false,
                supportsBinary: false
              });
            }, 1000);
            
            ws.onopen = function() {
              ws.send('test message');
              ws.send(new ArrayBuffer(8));
            };
            
            let textReceived = false;
            let binaryReceived = false;
            
            ws.onmessage = function(event) {
              if (typeof event.data === 'string') {
                textReceived = true;
              } else if (event.data instanceof ArrayBuffer) {
                binaryReceived = true;
              }
              
              if (textReceived && binaryReceived) {
                clearTimeout(timeout);
                ws.close();
                resolve({
                  success: true,
                  supportsText: textReceived,
                  supportsBinary: binaryReceived
                });
              }
            };
            
            ws.onerror = function() {
              clearTimeout(timeout);
              resolve({
                success: false,
                error: 'WebSocket error',
                supportsText: textReceived,
                supportsBinary: binaryReceived
              });
            };
            
          } catch (error) {
            resolve({
              success: false,
              error: error.message,
              supportsText: false,
              supportsBinary: false
            });
          }
        });
      });

      expect(messageResult.supportsText || messageResult.supportsBinary).toBe(true);
    });
  });

  test.describe('CORS Handling', () => {
    test('should handle CORS preflight requests', async ({ page }) => {
      const corsResult = await page.evaluate(async () => {
        try {
          const response = await fetch('https://httpbin.org/get', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-Custom-Header': 'test-value'
            }
          });
          
          return {
            success: true,
            status: response.status,
            supportsCORS: true
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            supportsCORS: error.message.includes('CORS') || error.message.includes('Cross-Origin')
          };
        }
      });

      expect(corsResult.supportsCORS).toBeDefined();
    });

    test('should handle different CORS scenarios', async ({ page }) => {
      const corsScenarios = await page.evaluate(async () => {
        const scenarios = [
          {
            name: 'simple-get',
            url: 'https://httpbin.org/get',
            options: { method: 'GET' }
          },
          {
            name: 'post-with-json',
            url: 'https://httpbin.org/post',
            options: {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ test: 'data' })
            }
          },
          {
            name: 'custom-headers',
            url: 'https://httpbin.org/get',
            options: {
              method: 'GET',
              headers: { 'X-Custom-Header': 'custom-value' }
            }
          }
        ];
        
        const results = [];
        
        for (const scenario of scenarios) {
          try {
            const response = await fetch(scenario.url, scenario.options);
            results.push({
              name: scenario.name,
              success: true,
              status: response.status
            });
          } catch (error) {
            results.push({
              name: scenario.name,
              success: false,
              error: error.message
            });
          }
        }
        
        return results;
      });

      expect(corsScenarios).toHaveLength(3);
      corsScenarios.forEach(result => {
        expect(result.success !== undefined).toBe(true);
      });
    });
  });

  test.describe('Request Timeout Handling', () => {
    test('should handle request timeouts with Fetch API', async ({ page }) => {
      test.skip(!browserCapabilities.fetch, 'Fetch API not supported in this browser');
      
      const timeoutResult = await page.evaluate(async () => {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 1000);
          
          const response = await fetch('https://httpbin.org/delay/2', {
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          return {
            success: true,
            supportsAbortController: true
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            supportsAbortController: 'AbortController' in window,
            isAbortError: error.name === 'AbortError'
          };
        }
      });

      expect(timeoutResult.supportsAbortController).toBe(true);
    });

    test('should handle request timeouts with XMLHttpRequest', async ({ page }) => {
      const xhrTimeoutResult = await page.evaluate(() => {
        return new Promise((resolve) => {
          try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://httpbin.org/delay/2', true);
            xhr.timeout = 1000;
            
            xhr.ontimeout = function() {
              resolve({
                success: false,
                error: 'Request timed out',
                supportsTimeout: true,
                isTimeoutError: true
              });
            };
            
            xhr.onload = function() {
              resolve({
                success: true,
                supportsTimeout: true,
                isTimeoutError: false
              });
            };
            
            xhr.onerror = function() {
              resolve({
                success: false,
                error: 'Request failed',
                supportsTimeout: 'timeout' in xhr,
                isTimeoutError: false
              });
            };
            
            xhr.send();
          } catch (error) {
            resolve({
              success: false,
              error: error.message,
              supportsTimeout: false,
              isTimeoutError: false
            });
          }
        });
      });

      expect(xhrTimeoutResult.supportsTimeout).toBe(true);
    });
  });

  test.describe('Browser-Specific Network Behavior', () => {
    test('should handle Safari network security restrictions', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari', 'Safari-specific test');
      
      const safariResult = await page.evaluate(async () => {
        try {
          // Safari has stricter CORS policies
          const response = await fetch('http://example.com/api', {
            mode: 'cors'
          });
          
          return {
            success: true,
            strictCORS: false
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            strictCORS: error.message.includes('CORS') || error.message.includes('blocked')
          };
        }
      });

      expect(safariResult.strictCORS !== undefined).toBe(true);
    });

    test('should handle Firefox mixed content policies', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');
      
      const firefoxResult = await page.evaluate(async () => {
        try {
          // Firefox blocks mixed content by default
          const response = await fetch('http://httpbin.org/get');
          
          return {
            success: true,
            blocksMixedContent: false
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            blocksMixedContent: error.message.includes('mixed content') || error.message.includes('blocked')
          };
        }
      });

      expect(firefoxResult.blocksMixedContent !== undefined).toBe(true);
    });

    test('should handle Chrome network optimizations', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');
      
      const chromeResult = await page.evaluate(async () => {
        try {
          // Test HTTP/2 support
          const response = await fetch('https://httpbin.org/get');
          
          return {
            success: true,
            status: response.status,
            supportsHTTP2: true // Chrome supports HTTP/2
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            supportsHTTP2: false
          };
        }
      });

      expect(chromeResult.supportsHTTP2).toBe(true);
    });
  });

  test.describe('Network Error Handling', () => {
    test('should handle network connectivity errors', async ({ page }) => {
      const networkErrorResult = await page.evaluate(async () => {
        try {
          const response = await fetch('https://nonexistent-domain-12345.com');
          
          return {
            success: true,
            error: null
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            errorType: error.name,
            isNetworkError: error.message.includes('network') || error.message.includes('fetch')
          };
        }
      });

      expect(networkErrorResult.success).toBe(false);
      expect(networkErrorResult.error).toBeTruthy();
    });

    test('should handle HTTP status codes consistently', async ({ page }) => {
      const statusCodeResult = await page.evaluate(async () => {
        const testCodes = [200, 404, 500];
        const results = [];
        
        for (const code of testCodes) {
          try {
            const response = await fetch(`https://httpbin.org/status/${code}`);
            results.push({
              code,
              success: true,
              status: response.status,
              statusText: response.statusText,
              ok: response.ok
            });
          } catch (error) {
            results.push({
              code,
              success: false,
              error: error.message
            });
          }
        }
        
        return results;
      });

      expect(statusCodeResult).toHaveLength(3);
      
      const successResult = statusCodeResult.find(r => r.code === 200);
      expect(successResult?.ok).toBe(true);
      
      const notFoundResult = statusCodeResult.find(r => r.code === 404);
      expect(notFoundResult?.ok).toBe(false);
      
      const errorResult = statusCodeResult.find(r => r.code === 500);
      expect(errorResult?.ok).toBe(false);
    });
  });

  test.describe('Mobile Network Considerations', () => {
    test('should handle mobile network conditions', async ({ page }) => {
      test.skip(!browserInfo.isMobile, 'Mobile-specific test');
      
      const mobileNetworkResult = await page.evaluate(async () => {
        try {
          // Test connection info API
          const connection = (navigator as any).connection || 
                            (navigator as any).mozConnection || 
                            (navigator as any).webkitConnection;
          
          const response = await fetch('https://httpbin.org/get');
          
          return {
            success: true,
            hasConnectionAPI: !!connection,
            effectiveType: connection?.effectiveType || 'unknown',
            downlink: connection?.downlink || 'unknown',
            rtt: connection?.rtt || 'unknown'
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            hasConnectionAPI: false
          };
        }
      });

      expect(mobileNetworkResult.hasConnectionAPI !== undefined).toBe(true);
    });

    test('should handle mobile data savings mode', async ({ page }) => {
      test.skip(!browserInfo.isMobile, 'Mobile-specific test');
      
      const dataSavingResult = await page.evaluate(() => {
        try {
          const connection = (navigator as any).connection || 
                            (navigator as any).mozConnection || 
                            (navigator as any).webkitConnection;
          
          return {
            hasConnectionAPI: !!connection,
            saveData: connection?.saveData || false,
            effectiveType: connection?.effectiveType || 'unknown'
          };
        } catch (error) {
          return {
            hasConnectionAPI: false,
            saveData: false,
            effectiveType: 'unknown',
            error: error.message
          };
        }
      });

      expect(dataSavingResult.hasConnectionAPI !== undefined).toBe(true);
    });
  });
});
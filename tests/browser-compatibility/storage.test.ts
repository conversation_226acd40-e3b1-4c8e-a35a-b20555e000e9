import { browserTest as test, expect, BrowserInfo, BrowserCapabilities, StorageHelper } from '../utils/browser-config';

test.describe('Browser Storage Compatibility', () => {
  let browserInfo: BrowserInfo;
  let browserCapabilities: BrowserCapabilities;
  let storageHelper: StorageHelper;

  test.beforeEach(async ({ page, browserInfo: info, browserCapabilities: caps, storageHelper: helper }) => {
    browserInfo = info;
    browserCapabilities = caps;
    storageHelper = helper;
    
    // Clear all storage before each test
    await StorageHelper.clearAllStorage(page);
    
    // Navigate to the application
    await page.goto('/');
  });

  test.describe('LocalStorage Compatibility', () => {
    test('should support localStorage operations', async ({ page }) => {
      test.skip(!browserCapabilities.localStorage, 'LocalStorage not supported in this browser');
      
      await page.evaluate(() => {
        localStorage.setItem('test-key', 'test-value');
        localStorage.setItem('complex-object', JSON.stringify({ id: 1, name: 'test' }));
      });

      const values = await page.evaluate(() => ({
        simple: localStorage.getItem('test-key'),
        complex: localStorage.getItem('complex-object')
      }));

      expect(values.simple).toBe('test-value');
      expect(JSON.parse(values.complex!)).toEqual({ id: 1, name: 'test' });
    });

    test('should handle localStorage size limits', async ({ page }) => {
      test.skip(!browserCapabilities.localStorage, 'LocalStorage not supported in this browser');
      
      const result = await page.evaluate(() => {
        try {
          const largeString = 'x'.repeat(10 * 1024 * 1024); // 10MB
          localStorage.setItem('large-item', largeString);
          return { success: true, error: null };
        } catch (error) {
          return { success: false, error: error.message };
        }
      });

      // Different browsers have different localStorage limits
      if (browserInfo.name === 'safari') {
        expect(result.success).toBe(false);
        expect(result.error).toContain('quota');
      } else {
        // Chrome/Firefox typically allow this size
        expect(result.success || result.error).toBeDefined();
      }
    });

    test('should persist localStorage across page reloads', async ({ page }) => {
      test.skip(!browserCapabilities.localStorage, 'LocalStorage not supported in this browser');
      
      await page.evaluate(() => {
        localStorage.setItem('persist-test', 'should-persist');
      });

      await page.reload();

      const persistedValue = await page.evaluate(() => localStorage.getItem('persist-test'));
      expect(persistedValue).toBe('should-persist');
    });

    test('should handle localStorage in incognito/private mode', async ({ page }) => {
      test.skip(!browserCapabilities.localStorage, 'LocalStorage not supported in this browser');
      
      const result = await page.evaluate(() => {
        try {
          localStorage.setItem('private-test', 'private-value');
          return localStorage.getItem('private-test');
        } catch (error) {
          return null;
        }
      });

      // In most browsers, localStorage works in incognito but may have restrictions
      expect(result).toBeDefined();
    });
  });

  test.describe('SessionStorage Compatibility', () => {
    test('should support sessionStorage operations', async ({ page }) => {
      test.skip(!browserCapabilities.sessionStorage, 'SessionStorage not supported in this browser');
      
      await page.evaluate(() => {
        sessionStorage.setItem('session-key', 'session-value');
        sessionStorage.setItem('session-object', JSON.stringify({ session: true }));
      });

      const values = await page.evaluate(() => ({
        simple: sessionStorage.getItem('session-key'),
        complex: sessionStorage.getItem('session-object')
      }));

      expect(values.simple).toBe('session-value');
      expect(JSON.parse(values.complex!)).toEqual({ session: true });
    });

    test('should clear sessionStorage on new tab', async ({ context, page }) => {
      test.skip(!browserCapabilities.sessionStorage, 'SessionStorage not supported in this browser');
      
      await page.evaluate(() => {
        sessionStorage.setItem('tab-specific', 'original-tab');
      });

      const newPage = await context.newPage();
      await newPage.goto('/');

      const newTabValue = await newPage.evaluate(() => sessionStorage.getItem('tab-specific'));
      expect(newTabValue).toBeNull();

      const originalTabValue = await page.evaluate(() => sessionStorage.getItem('tab-specific'));
      expect(originalTabValue).toBe('original-tab');

      await newPage.close();
    });
  });

  test.describe('Cookie Compatibility', () => {
    test('should support cookie operations', async ({ page }) => {
      test.skip(!browserCapabilities.cookies, 'Cookies not supported in this browser');
      
      await page.evaluate(() => {
        document.cookie = 'test-cookie=test-value; path=/';
        document.cookie = 'secure-cookie=secure-value; secure; path=/';
      });

      const cookies = await page.evaluate(() => document.cookie);
      expect(cookies).toContain('test-cookie=test-value');
      
      // Secure cookies might not be visible in non-HTTPS context
      if (page.url().startsWith('https://')) {
        expect(cookies).toContain('secure-cookie=secure-value');
      }
    });

    test('should handle cookie size limits', async ({ page }) => {
      test.skip(!browserCapabilities.cookies, 'Cookies not supported in this browser');
      
      const result = await page.evaluate(() => {
        try {
          const largeValue = 'x'.repeat(5000); // 5KB cookie
          document.cookie = `large-cookie=${largeValue}; path=/`;
          return { success: true, error: null };
        } catch (error) {
          return { success: false, error: error.message };
        }
      });

      // Different browsers have different cookie size limits
      if (browserInfo.name === 'safari') {
        expect(result.success || result.error).toBeDefined();
      } else {
        expect(result.success || result.error).toBeDefined();
      }
    });

    test('should handle SameSite cookie attributes', async ({ page }) => {
      test.skip(!browserCapabilities.cookies, 'Cookies not supported in this browser');
      
      await page.evaluate(() => {
        document.cookie = 'samesite-strict=value1; SameSite=Strict; path=/';
        document.cookie = 'samesite-lax=value2; SameSite=Lax; path=/';
        document.cookie = 'samesite-none=value3; SameSite=None; Secure; path=/';
      });

      const cookies = await page.context().cookies();
      const sameSiteCookies = cookies.filter(cookie => cookie.name.startsWith('samesite-'));
      
      expect(sameSiteCookies.length).toBeGreaterThan(0);
    });
  });

  test.describe('IndexedDB Compatibility', () => {
    test('should support IndexedDB operations', async ({ page }) => {
      test.skip(!browserCapabilities.indexedDB, 'IndexedDB not supported in this browser');
      
      const result = await page.evaluate(async () => {
        return new Promise((resolve) => {
          const request = indexedDB.open('test-db', 1);
          
          request.onerror = () => resolve({ success: false, error: 'Failed to open database' });
          
          request.onsuccess = () => {
            const db = request.result;
            db.close();
            resolve({ success: true, error: null });
          };
          
          request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            db.createObjectStore('test-store', { keyPath: 'id' });
          };
        });
      });

      expect(result.success).toBe(true);
    });

    test('should handle IndexedDB transactions', async ({ page }) => {
      test.skip(!browserCapabilities.indexedDB, 'IndexedDB not supported in this browser');
      
      const result = await page.evaluate(async () => {
        return new Promise((resolve) => {
          const request = indexedDB.open('transaction-test-db', 1);
          
          request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            db.createObjectStore('items', { keyPath: 'id' });
          };
          
          request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction(['items'], 'readwrite');
            const store = transaction.objectStore('items');
            
            const addRequest = store.add({ id: 1, name: 'Test Item' });
            
            addRequest.onsuccess = () => {
              const getRequest = store.get(1);
              getRequest.onsuccess = () => {
                db.close();
                resolve({ success: true, data: getRequest.result });
              };
            };
            
            addRequest.onerror = () => resolve({ success: false, error: 'Failed to add item' });
          };
        });
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ id: 1, name: 'Test Item' });
    });
  });

  test.describe('Cross-Browser Storage Behavior', () => {
    test('should handle storage events', async ({ page, context }) => {
      test.skip(!browserCapabilities.localStorage, 'LocalStorage not supported in this browser');
      
      const secondPage = await context.newPage();
      await secondPage.goto('/');

      let eventFired = false;
      await secondPage.evaluate(() => {
        window.addEventListener('storage', (e) => {
          (window as any).storageEventFired = true;
          (window as any).storageEventData = {
            key: e.key,
            newValue: e.newValue,
            oldValue: e.oldValue
          };
        });
      });

      await page.evaluate(() => {
        localStorage.setItem('storage-event-test', 'new-value');
      });

      await secondPage.waitForTimeout(100);

      const eventResult = await secondPage.evaluate(() => ({
        fired: (window as any).storageEventFired,
        data: (window as any).storageEventData
      }));

      expect(eventResult.fired).toBe(true);
      expect(eventResult.data.key).toBe('storage-event-test');
      expect(eventResult.data.newValue).toBe('new-value');

      await secondPage.close();
    });

    test('should handle storage in different domains', async ({ page }) => {
      test.skip(!browserCapabilities.localStorage, 'LocalStorage not supported in this browser');
      
      await page.evaluate(() => {
        localStorage.setItem('domain-test', 'localhost-value');
      });

      // Navigate to different origin (if available)
      try {
        await page.goto('http://127.0.0.1:8000/');
        
        const differentDomainValue = await page.evaluate(() => 
          localStorage.getItem('domain-test')
        );
        
        expect(differentDomainValue).toBeNull();
      } catch (error) {
        // Skip if different domain not available
        test.skip(true, 'Different domain not available for testing');
      }
    });
  });

  test.describe('Browser-Specific Storage Quirks', () => {
    test('should handle Safari private mode storage limitations', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari', 'Safari-specific test');
      
      const result = await page.evaluate(() => {
        try {
          // Safari in private mode has a 0-byte localStorage quota
          localStorage.setItem('safari-private-test', 'value');
          return { success: true, error: null };
        } catch (error) {
          return { success: false, error: error.message };
        }
      });

      expect(result.success || result.error).toBeDefined();
    });

    test('should handle Firefox localStorage in private mode', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');
      
      const result = await page.evaluate(() => {
        try {
          localStorage.setItem('firefox-private-test', 'value');
          return { success: true, value: localStorage.getItem('firefox-private-test') };
        } catch (error) {
          return { success: false, error: error.message };
        }
      });

      expect(result.success).toBe(true);
      expect(result.value).toBe('value');
    });

    test('should handle Chrome storage persistence', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');
      
      await page.evaluate(() => {
        localStorage.setItem('chrome-persistence-test', 'persistent-value');
      });

      await page.reload();

      const persistedValue = await page.evaluate(() => 
        localStorage.getItem('chrome-persistence-test')
      );
      
      expect(persistedValue).toBe('persistent-value');
    });
  });
});
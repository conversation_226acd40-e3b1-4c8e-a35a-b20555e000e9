import { browserTest as test, expect, BrowserInfo, ResponsiveHelper } from '../utils/browser-config';

test.describe('Browser Responsive Design Compatibility', () => {
  let browserInfo: BrowserInfo;

  test.beforeEach(async ({ page, browserInfo: info }) => {
    browserInfo = info;
    await page.goto('/');
  });

  test.describe('Viewport and Media Query Support', () => {
    test('should handle viewport meta tag', async ({ page }) => {
      const viewportResult = await page.evaluate(() => {
        const viewport = document.querySelector('meta[name="viewport"]');
        return {
          hasViewport: !!viewport,
          content: viewport?.getAttribute('content') || '',
          innerWidth: window.innerWidth,
          innerHeight: window.innerHeight,
          devicePixelRatio: window.devicePixelRatio
        };
      });

      expect(viewportResult.hasViewport).toBe(true);
      expect(viewportResult.innerWidth).toBeGreaterThan(0);
      expect(viewportResult.innerHeight).toBeGreaterThan(0);
      expect(viewportResult.devicePixelRatio).toBeGreaterThan(0);
    });

    test('should support CSS media queries', async ({ page }) => {
      const mediaQueryResult = await page.evaluate(() => {
        const testQueries = [
          '(max-width: 768px)',
          '(min-width: 769px)',
          '(orientation: portrait)',
          '(orientation: landscape)',
          '(prefers-color-scheme: dark)',
          '(prefers-color-scheme: light)',
          '(hover: hover)',
          '(pointer: fine)',
          '(pointer: coarse)'
        ];
        
        const results = testQueries.map(query => ({
          query,
          matches: window.matchMedia(query).matches,
          supportsMatchMedia: typeof window.matchMedia === 'function'
        }));
        
        return {
          results,
          supportsMatchMedia: typeof window.matchMedia === 'function'
        };
      });

      expect(mediaQueryResult.supportsMatchMedia).toBe(true);
      expect(mediaQueryResult.results.length).toBe(9);
    });

    test('should respond to viewport size changes', async ({ page }) => {
      const viewportSizes = [
        { width: 320, height: 568 },  // Mobile portrait
        { width: 768, height: 1024 }, // Tablet portrait
        { width: 1024, height: 768 }, // Tablet landscape
        { width: 1920, height: 1080 } // Desktop
      ];

      for (const size of viewportSizes) {
        await page.setViewportSize(size);
        await page.waitForTimeout(100);

        const currentSize = await page.evaluate(() => ({
          width: window.innerWidth,
          height: window.innerHeight
        }));

        expect(currentSize.width).toBe(size.width);
        expect(currentSize.height).toBe(size.height);
      }
    });

    test('should handle device pixel ratio correctly', async ({ page }) => {
      const pixelRatioResult = await page.evaluate(() => {
        return {
          devicePixelRatio: window.devicePixelRatio,
          supportsDevicePixelRatio: 'devicePixelRatio' in window,
          screenWidth: screen.width,
          screenHeight: screen.height,
          availWidth: screen.availWidth,
          availHeight: screen.availHeight
        };
      });

      expect(pixelRatioResult.supportsDevicePixelRatio).toBe(true);
      expect(pixelRatioResult.devicePixelRatio).toBeGreaterThan(0);
      expect(pixelRatioResult.screenWidth).toBeGreaterThan(0);
      expect(pixelRatioResult.screenHeight).toBeGreaterThan(0);
    });
  });

  test.describe('CSS Layout and Flexbox Support', () => {
    test('should support modern CSS layout methods', async ({ page }) => {
      const layoutSupport = await page.evaluate(() => {
        const testElement = document.createElement('div');
        document.body.appendChild(testElement);
        
        const styles = {
          flexbox: false,
          grid: false,
          transforms: false,
          transitions: false,
          animations: false
        };
        
        try {
          testElement.style.display = 'flex';
          styles.flexbox = testElement.style.display === 'flex';
          
          testElement.style.display = 'grid';
          styles.grid = testElement.style.display === 'grid';
          
          testElement.style.transform = 'rotate(45deg)';
          styles.transforms = testElement.style.transform === 'rotate(45deg)';
          
          testElement.style.transition = 'opacity 0.3s ease';
          styles.transitions = testElement.style.transition.includes('opacity');
          
          testElement.style.animation = 'fadeIn 1s ease';
          styles.animations = testElement.style.animation.includes('fadeIn');
          
        } catch (error) {
          console.error('CSS support test failed:', error);
        } finally {
          document.body.removeChild(testElement);
        }
        
        return styles;
      });

      expect(layoutSupport.flexbox).toBe(true);
      expect(layoutSupport.grid).toBe(true);
      expect(layoutSupport.transforms).toBe(true);
      expect(layoutSupport.transitions).toBe(true);
    });

    test('should handle flexbox layout correctly', async ({ page }) => {
      const flexboxHtml = `
        <div style="display: flex; justify-content: space-between; align-items: center; height: 100px;">
          <div id="flex-item-1" style="flex: 1; background: red;">Item 1</div>
          <div id="flex-item-2" style="flex: 2; background: blue;">Item 2</div>
          <div id="flex-item-3" style="flex: 1; background: green;">Item 3</div>
        </div>
      `;

      await page.setContent(flexboxHtml);

      const flexboxLayout = await page.evaluate(() => {
        const container = document.querySelector('div');
        const item1 = document.getElementById('flex-item-1');
        const item2 = document.getElementById('flex-item-2');
        const item3 = document.getElementById('flex-item-3');
        
        return {
          containerDisplay: window.getComputedStyle(container!).display,
          item1Width: item1!.offsetWidth,
          item2Width: item2!.offsetWidth,
          item3Width: item3!.offsetWidth,
          totalWidth: container!.offsetWidth
        };
      });

      expect(flexboxLayout.containerDisplay).toBe('flex');
      expect(flexboxLayout.item2Width).toBeGreaterThan(flexboxLayout.item1Width);
      expect(flexboxLayout.item2Width).toBeGreaterThan(flexboxLayout.item3Width);
    });

    test('should handle CSS Grid layout', async ({ page }) => {
      const gridHtml = `
        <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 10px; height: 200px;">
          <div id="grid-item-1" style="background: red;">Item 1</div>
          <div id="grid-item-2" style="background: blue;">Item 2</div>
          <div id="grid-item-3" style="background: green;">Item 3</div>
        </div>
      `;

      await page.setContent(gridHtml);

      const gridLayout = await page.evaluate(() => {
        const container = document.querySelector('div');
        const item1 = document.getElementById('grid-item-1');
        const item2 = document.getElementById('grid-item-2');
        const item3 = document.getElementById('grid-item-3');
        
        return {
          containerDisplay: window.getComputedStyle(container!).display,
          item1Width: item1!.offsetWidth,
          item2Width: item2!.offsetWidth,
          item3Width: item3!.offsetWidth,
          gridTemplateColumns: window.getComputedStyle(container!).gridTemplateColumns
        };
      });

      expect(gridLayout.containerDisplay).toBe('grid');
      expect(gridLayout.item2Width).toBeGreaterThan(gridLayout.item1Width);
      expect(gridLayout.gridTemplateColumns).toBeTruthy();
    });
  });

  test.describe('Touch and Pointer Events', () => {
    test('should detect touch support correctly', async ({ page }) => {
      const touchSupport = await page.evaluate(() => {
        return {
          hasTouch: 'ontouchstart' in window,
          hasTouchEvents: 'TouchEvent' in window,
          hasPointerEvents: 'PointerEvent' in window,
          maxTouchPoints: navigator.maxTouchPoints || 0,
          msMaxTouchPoints: (navigator as any).msMaxTouchPoints || 0
        };
      });

      if (browserInfo.isMobile) {
        expect(touchSupport.hasTouch).toBe(true);
        expect(touchSupport.maxTouchPoints).toBeGreaterThan(0);
      } else {
        expect(touchSupport.hasTouch !== undefined).toBe(true);
      }
    });

    test('should handle pointer events', async ({ page }) => {
      const pointerHtml = `
        <div id="pointer-test" style="width: 200px; height: 200px; background: lightblue;">
          Touch/Click me
        </div>
      `;

      await page.setContent(pointerHtml);

      const pointerResult = await page.evaluate(() => {
        const element = document.getElementById('pointer-test');
        let pointerEvents = 0;
        let touchEvents = 0;
        let mouseEvents = 0;
        
        const pointerHandler = () => pointerEvents++;
        const touchHandler = () => touchEvents++;
        const mouseHandler = () => mouseEvents++;
        
        element!.addEventListener('pointerdown', pointerHandler);
        element!.addEventListener('touchstart', touchHandler);
        element!.addEventListener('mousedown', mouseHandler);
        
        // Simulate events
        element!.dispatchEvent(new Event('pointerdown'));
        element!.dispatchEvent(new Event('touchstart'));
        element!.dispatchEvent(new Event('mousedown'));
        
        return {
          pointerEvents,
          touchEvents,
          mouseEvents,
          supportsPointerEvents: 'PointerEvent' in window
        };
      });

      expect(pointerResult.supportsPointerEvents).toBe(true);
      expect(pointerResult.pointerEvents).toBe(1);
      expect(pointerResult.mouseEvents).toBe(1);
    });
  });

  test.describe('Browser-Specific Responsive Behavior', () => {
    test('should handle Safari mobile viewport quirks', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari' || !browserInfo.isMobile, 'Safari mobile specific test');
      
      const safariViewport = await page.evaluate(() => {
        return {
          visualViewportWidth: window.visualViewport?.width || window.innerWidth,
          visualViewportHeight: window.visualViewport?.height || window.innerHeight,
          innerWidth: window.innerWidth,
          innerHeight: window.innerHeight,
          outerWidth: window.outerWidth,
          outerHeight: window.outerHeight,
          supportsVisualViewport: 'visualViewport' in window
        };
      });

      expect(safariViewport.supportsVisualViewport).toBe(true);
      expect(safariViewport.visualViewportWidth).toBeGreaterThan(0);
      expect(safariViewport.visualViewportHeight).toBeGreaterThan(0);
    });

    test('should handle Firefox responsive design mode', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');
      
      const firefoxResponsive = await page.evaluate(() => {
        return {
          innerWidth: window.innerWidth,
          innerHeight: window.innerHeight,
          devicePixelRatio: window.devicePixelRatio,
          orientation: screen.orientation?.angle || 0,
          supportsOrientation: 'orientation' in screen
        };
      });

      expect(firefoxResponsive.innerWidth).toBeGreaterThan(0);
      expect(firefoxResponsive.innerHeight).toBeGreaterThan(0);
      expect(firefoxResponsive.devicePixelRatio).toBeGreaterThan(0);
    });

    test('should handle Chrome mobile emulation', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');
      
      const chromeEmulation = await page.evaluate(() => {
        return {
          innerWidth: window.innerWidth,
          innerHeight: window.innerHeight,
          devicePixelRatio: window.devicePixelRatio,
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          touchPoints: navigator.maxTouchPoints
        };
      });

      expect(chromeEmulation.innerWidth).toBeGreaterThan(0);
      expect(chromeEmulation.innerHeight).toBeGreaterThan(0);
      expect(chromeEmulation.devicePixelRatio).toBeGreaterThan(0);
    });
  });

  test.describe('Orientation and Screen Changes', () => {
    test('should handle orientation changes', async ({ page }) => {
      const orientationResult = await page.evaluate(() => {
        return {
          supportsOrientation: 'orientation' in screen,
          orientation: screen.orientation?.angle || 0,
          orientationType: screen.orientation?.type || 'unknown',
          supportsOrientationChange: 'onorientationchange' in window
        };
      });

      expect(orientationResult.supportsOrientation || orientationResult.supportsOrientationChange).toBe(true);
    });

    test('should handle different screen sizes', async ({ page }) => {
      const screenSizes = [
        { width: 320, height: 568, name: 'iPhone SE' },
        { width: 375, height: 667, name: 'iPhone 6/7/8' },
        { width: 414, height: 896, name: 'iPhone 11' },
        { width: 768, height: 1024, name: 'iPad' },
        { width: 1024, height: 1366, name: 'iPad Pro' },
        { width: 1920, height: 1080, name: 'Desktop HD' }
      ];

      for (const size of screenSizes) {
        await page.setViewportSize({ width: size.width, height: size.height });
        await page.waitForTimeout(100);

        const currentViewport = await page.evaluate(() => ({
          width: window.innerWidth,
          height: window.innerHeight,
          aspectRatio: window.innerWidth / window.innerHeight
        }));

        expect(currentViewport.width).toBe(size.width);
        expect(currentViewport.height).toBe(size.height);
        expect(currentViewport.aspectRatio).toBeGreaterThan(0);
      }
    });

    test('should handle high DPI screens', async ({ page }) => {
      const highDPIResult = await page.evaluate(() => {
        return {
          devicePixelRatio: window.devicePixelRatio,
          supportsHighDPI: window.devicePixelRatio > 1,
          screenWidth: screen.width,
          screenHeight: screen.height,
          colorDepth: screen.colorDepth,
          pixelDepth: screen.pixelDepth
        };
      });

      expect(highDPIResult.devicePixelRatio).toBeGreaterThan(0);
      expect(highDPIResult.screenWidth).toBeGreaterThan(0);
      expect(highDPIResult.screenHeight).toBeGreaterThan(0);
      expect(highDPIResult.colorDepth).toBeGreaterThan(0);
    });
  });

  test.describe('Responsive Images and Media', () => {
    test('should handle responsive images', async ({ page }) => {
      const responsiveImageHtml = `
        <img id="responsive-img" 
             src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwZiIvPjwvc3ZnPg=="
             srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzBmMCIvPjwvc3ZnPg== 2x"
             sizes="(max-width: 768px) 100vw, 50vw"
             style="max-width: 100%; height: auto;" />
      `;

      await page.setContent(responsiveImageHtml);

      const imageResult = await page.evaluate(() => {
        const img = document.getElementById('responsive-img') as HTMLImageElement;
        return {
          hasSrcset: img.srcset !== '',
          hasSizes: img.sizes !== '',
          naturalWidth: img.naturalWidth,
          naturalHeight: img.naturalHeight,
          displayWidth: img.width,
          displayHeight: img.height,
          currentSrc: img.currentSrc !== ''
        };
      });

      expect(imageResult.hasSrcset).toBe(true);
      expect(imageResult.hasSizes).toBe(true);
      expect(imageResult.naturalWidth).toBeGreaterThan(0);
      expect(imageResult.naturalHeight).toBeGreaterThan(0);
    });

    test('should handle picture element', async ({ page }) => {
      const pictureHtml = `
        <picture>
          <source media="(min-width: 800px)" 
                  srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzAwZiIvPjwvc3ZnPg==">
          <source media="(min-width: 400px)" 
                  srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzBmMCIvPjwvc3ZnPg==">
          <img id="picture-img" 
               src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2YwMCIvPjwvc3ZnPg=="
               alt="Responsive image">
        </picture>
      `;

      await page.setContent(pictureHtml);

      const pictureResult = await page.evaluate(() => {
        const picture = document.querySelector('picture');
        const img = document.getElementById('picture-img') as HTMLImageElement;
        const sources = picture!.querySelectorAll('source');
        
        return {
          hasPicture: !!picture,
          sourceCount: sources.length,
          imgCurrentSrc: img.currentSrc !== '',
          imgSrc: img.src !== '',
          supportsPicture: 'HTMLPictureElement' in window
        };
      });

      expect(pictureResult.hasPicture).toBe(true);
      expect(pictureResult.sourceCount).toBe(2);
      expect(pictureResult.supportsPicture).toBe(true);
    });
  });

  test.describe('Responsive Typography', () => {
    test('should handle viewport units', async ({ page }) => {
      const typographyHtml = `
        <div id="viewport-text" style="font-size: 4vw; line-height: 1.2; padding: 2vh;">
          This text uses viewport units
        </div>
      `;

      await page.setContent(typographyHtml);

      const viewportSizes = [
        { width: 320, height: 568 },
        { width: 768, height: 1024 },
        { width: 1920, height: 1080 }
      ];

      for (const size of viewportSizes) {
        await page.setViewportSize(size);
        await page.waitForTimeout(100);

        const typographyResult = await page.evaluate(() => {
          const element = document.getElementById('viewport-text');
          const styles = window.getComputedStyle(element!);
          return {
            fontSize: parseFloat(styles.fontSize),
            lineHeight: parseFloat(styles.lineHeight),
            padding: parseFloat(styles.paddingTop)
          };
        });

        expect(typographyResult.fontSize).toBeGreaterThan(0);
        expect(typographyResult.lineHeight).toBeGreaterThan(0);
        expect(typographyResult.padding).toBeGreaterThan(0);
      }
    });

    test('should handle responsive font scaling', async ({ page }) => {
      const fontScalingHtml = `
        <style>
          @media (max-width: 768px) {
            .responsive-text { font-size: 14px; }
          }
          @media (min-width: 769px) {
            .responsive-text { font-size: 18px; }
          }
        </style>
        <div id="responsive-text" class="responsive-text">
          Responsive text
        </div>
      `;

      await page.setContent(fontScalingHtml);

      // Test mobile size
      await page.setViewportSize({ width: 320, height: 568 });
      await page.waitForTimeout(100);

      const mobileFontSize = await page.evaluate(() => {
        const element = document.getElementById('responsive-text');
        return parseFloat(window.getComputedStyle(element!).fontSize);
      });

      // Test desktop size
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.waitForTimeout(100);

      const desktopFontSize = await page.evaluate(() => {
        const element = document.getElementById('responsive-text');
        return parseFloat(window.getComputedStyle(element!).fontSize);
      });

      expect(mobileFontSize).toBeGreaterThan(0);
      expect(desktopFontSize).toBeGreaterThan(0);
      expect(desktopFontSize).toBeGreaterThan(mobileFontSize);
    });
  });
});
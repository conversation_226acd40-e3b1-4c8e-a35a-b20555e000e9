import { browserTest as test, expect, BrowserInfo, BrowserCapabilities } from '../utils/browser-config';

test.describe('Browser Feature Detection', () => {
  let browserInfo: BrowserInfo;
  let browserCapabilities: BrowserCapabilities;

  test.beforeEach(async ({ page, browserInfo: info, browserCapabilities: caps }) => {
    browserInfo = info;
    browserCapabilities = caps;
    await page.goto('/');
  });

  test.describe('JavaScript API Feature Detection', () => {
    test('should detect ES6+ features', async ({ page }) => {
      const es6Features = await page.evaluate(() => {
        const features = {
          arrow_functions: false,
          classes: false,
          let_const: false,
          template_literals: false,
          destructuring: false,
          spread_operator: false,
          promises: false,
          async_await: false,
          symbols: false,
          iterators: false,
          generators: false,
          maps_sets: false,
          proxy: false,
          modules: false
        };

        try {
          // Test arrow functions
          eval('() => {}');
          features.arrow_functions = true;
        } catch (e) {}

        try {
          // Test classes
          eval('class Test {}');
          features.classes = true;
        } catch (e) {}

        try {
          // Test let/const
          eval('let x = 1; const y = 2;');
          features.let_const = true;
        } catch (e) {}

        try {
          // Test template literals
          eval('`template ${1} literal`');
          features.template_literals = true;
        } catch (e) {}

        try {
          // Test destructuring
          eval('const [a, b] = [1, 2];');
          features.destructuring = true;
        } catch (e) {}

        try {
          // Test spread operator
          eval('const arr = [1, 2, 3]; const newArr = [...arr];');
          features.spread_operator = true;
        } catch (e) {}

        // Test Promises
        features.promises = typeof Promise !== 'undefined';

        // Test async/await
        try {
          eval('async function test() { await Promise.resolve(); }');
          features.async_await = true;
        } catch (e) {}

        // Test Symbols
        features.symbols = typeof Symbol !== 'undefined';

        // Test Iterators
        features.iterators = typeof Symbol !== 'undefined' && Symbol.iterator !== undefined;

        // Test Generators
        try {
          eval('function* gen() { yield 1; }');
          features.generators = true;
        } catch (e) {}

        // Test Maps and Sets
        features.maps_sets = typeof Map !== 'undefined' && typeof Set !== 'undefined';

        // Test Proxy
        features.proxy = typeof Proxy !== 'undefined';

        // Test ES6 modules support
        features.modules = typeof Symbol !== 'undefined' && 'Symbol' in window;

        return features;
      });

      // Modern browsers should support most ES6 features
      expect(es6Features.arrow_functions).toBe(true);
      expect(es6Features.classes).toBe(true);
      expect(es6Features.let_const).toBe(true);
      expect(es6Features.template_literals).toBe(true);
      expect(es6Features.promises).toBe(true);
      expect(es6Features.symbols).toBe(true);
      expect(es6Features.maps_sets).toBe(true);
    });

    test('should detect Web API features', async ({ page }) => {
      const webApiFeatures = await page.evaluate(() => {
        return {
          fetch: typeof fetch !== 'undefined',
          websockets: typeof WebSocket !== 'undefined',
          webworkers: typeof Worker !== 'undefined',
          serviceworkers: 'serviceWorker' in navigator,
          notifications: 'Notification' in window,
          geolocation: 'geolocation' in navigator,
          camera: 'mediaDevices' in navigator,
          webrtc: 'RTCPeerConnection' in window,
          webspeech: 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window,
          gamepad: 'getGamepads' in navigator,
          vibration: 'vibrate' in navigator,
          battery: 'getBattery' in navigator,
          fullscreen: 'requestFullscreen' in document.documentElement,
          pointer_lock: 'requestPointerLock' in document.documentElement,
          page_visibility: 'visibilityState' in document,
          intersection_observer: 'IntersectionObserver' in window,
          mutation_observer: 'MutationObserver' in window,
          resize_observer: 'ResizeObserver' in window,
          performance_observer: 'PerformanceObserver' in window
        };
      });

      // Basic APIs should be available
      expect(webApiFeatures.fetch).toBe(browserCapabilities.fetch);
      expect(webApiFeatures.websockets).toBe(browserCapabilities.websockets);
      expect(webApiFeatures.webworkers).toBe(browserCapabilities.webWorkers);
      expect(webApiFeatures.serviceworkers).toBe(browserCapabilities.serviceWorkers);
      expect(webApiFeatures.geolocation).toBe(browserCapabilities.geolocation);
      expect(webApiFeatures.webrtc).toBe(browserCapabilities.webRTC);
    });

    test('should detect DOM API features', async ({ page }) => {
      const domApiFeatures = await page.evaluate(() => {
        return {
          query_selector: 'querySelector' in document,
          query_selector_all: 'querySelectorAll' in document,
          get_elements_by_class: 'getElementsByClassName' in document,
          add_event_listener: 'addEventListener' in document,
          remove_event_listener: 'removeEventListener' in document,
          create_element: 'createElement' in document,
          custom_elements: 'customElements' in window,
          shadow_dom: 'attachShadow' in Element.prototype,
          template: 'content' in document.createElement('template'),
          dataset: 'dataset' in document.createElement('div'),
          classList: 'classList' in document.createElement('div'),
          matches: 'matches' in document.createElement('div'),
          closest: 'closest' in document.createElement('div'),
          append: 'append' in document.createElement('div'),
          prepend: 'prepend' in document.createElement('div'),
          remove: 'remove' in document.createElement('div'),
          replaceWith: 'replaceWith' in document.createElement('div')
        };
      });

      // Core DOM APIs should be available
      expect(domApiFeatures.query_selector).toBe(true);
      expect(domApiFeatures.query_selector_all).toBe(true);
      expect(domApiFeatures.add_event_listener).toBe(true);
      expect(domApiFeatures.create_element).toBe(true);
      expect(domApiFeatures.classList).toBe(true);
      expect(domApiFeatures.dataset).toBe(true);
      expect(domApiFeatures.custom_elements).toBe(browserCapabilities.customElements);
      expect(domApiFeatures.shadow_dom).toBe(browserCapabilities.shadowDOM);
    });

    test('should detect CSS feature support', async ({ page }) => {
      const cssFeatures = await page.evaluate(() => {
        const testElement = document.createElement('div');
        const style = testElement.style;
        
        const features = {
          flexbox: false,
          grid: false,
          transforms: false,
          transitions: false,
          animations: false,
          calc: false,
          css_variables: false,
          supports_api: false,
          object_fit: false,
          clip_path: false,
          filters: false,
          backdrop_filter: false,
          sticky_position: false,
          viewport_units: false
        };

        // Test flexbox
        style.display = 'flex';
        features.flexbox = style.display === 'flex';

        // Test grid
        style.display = 'grid';
        features.grid = style.display === 'grid';

        // Test transforms
        style.transform = 'rotate(45deg)';
        features.transforms = style.transform !== '';

        // Test transitions
        style.transition = 'opacity 0.3s ease';
        features.transitions = style.transition !== '';

        // Test animations
        style.animation = 'fadeIn 1s ease';
        features.animations = style.animation !== '';

        // Test calc()
        style.width = 'calc(100% - 20px)';
        features.calc = style.width !== '';

        // Test CSS variables
        style.setProperty('--test-var', 'red');
        features.css_variables = style.getPropertyValue('--test-var') === 'red';

        // Test CSS.supports API
        features.supports_api = 'CSS' in window && 'supports' in CSS;

        // Test object-fit
        style.objectFit = 'cover';
        features.object_fit = style.objectFit === 'cover';

        // Test clip-path
        style.clipPath = 'circle(50%)';
        features.clip_path = style.clipPath !== '';

        // Test filters
        style.filter = 'blur(5px)';
        features.filters = style.filter !== '';

        // Test backdrop-filter
        style.backdropFilter = 'blur(5px)';
        features.backdrop_filter = style.backdropFilter !== '';

        // Test sticky position
        style.position = 'sticky';
        features.sticky_position = style.position === 'sticky';

        // Test viewport units
        style.width = '50vw';
        features.viewport_units = style.width !== '';

        return features;
      });

      // Modern CSS features should be supported
      expect(cssFeatures.flexbox).toBe(true);
      expect(cssFeatures.grid).toBe(true);
      expect(cssFeatures.transforms).toBe(true);
      expect(cssFeatures.transitions).toBe(true);
      expect(cssFeatures.animations).toBe(true);
      expect(cssFeatures.calc).toBe(true);
      expect(cssFeatures.css_variables).toBe(true);
      expect(cssFeatures.supports_api).toBe(true);
      expect(cssFeatures.viewport_units).toBe(true);
    });
  });

  test.describe('Input and Form Features', () => {
    test('should detect HTML5 input types', async ({ page }) => {
      const inputTypes = await page.evaluate(() => {
        const input = document.createElement('input');
        const types = [
          'text', 'email', 'url', 'tel', 'search', 'password',
          'number', 'range', 'date', 'time', 'datetime-local',
          'month', 'week', 'color', 'file', 'hidden',
          'checkbox', 'radio', 'submit', 'reset', 'button'
        ];

        const supportedTypes: { [key: string]: boolean } = {};
        
        types.forEach(type => {
          input.type = type;
          supportedTypes[type] = input.type === type;
        });

        return supportedTypes;
      });

      // Basic input types should be supported
      expect(inputTypes.text).toBe(true);
      expect(inputTypes.email).toBe(true);
      expect(inputTypes.password).toBe(true);
      expect(inputTypes.number).toBe(true);
      expect(inputTypes.checkbox).toBe(true);
      expect(inputTypes.radio).toBe(true);
      expect(inputTypes.submit).toBe(true);
      expect(inputTypes.file).toBe(true);
    });

    test('should detect form validation features', async ({ page }) => {
      const formValidation = await page.evaluate(() => {
        const form = document.createElement('form');
        const input = document.createElement('input');
        
        return {
          checkValidity: 'checkValidity' in form,
          reportValidity: 'reportValidity' in form,
          setCustomValidity: 'setCustomValidity' in input,
          validity: 'validity' in input,
          validationMessage: 'validationMessage' in input,
          willValidate: 'willValidate' in input,
          formNoValidate: 'formNoValidate' in input,
          required: 'required' in input,
          pattern: 'pattern' in input,
          min: 'min' in input,
          max: 'max' in input,
          step: 'step' in input,
          maxLength: 'maxLength' in input,
          minLength: 'minLength' in input
        };
      });

      expect(formValidation.checkValidity).toBe(true);
      expect(formValidation.validity).toBe(true);
      expect(formValidation.validationMessage).toBe(true);
      expect(formValidation.required).toBe(true);
      expect(formValidation.pattern).toBe(true);
      expect(formValidation.maxLength).toBe(true);
    });

    test('should detect form element features', async ({ page }) => {
      const formElements = await page.evaluate(() => {
        return {
          datalist: 'HTMLDataListElement' in window,
          output: 'HTMLOutputElement' in window,
          progress: 'HTMLProgressElement' in window,
          meter: 'HTMLMeterElement' in window,
          details: 'HTMLDetailsElement' in window,
          summary: 'HTMLElement' in window,
          fieldset: 'HTMLFieldSetElement' in window,
          legend: 'HTMLLegendElement' in window,
          optgroup: 'HTMLOptGroupElement' in window,
          option: 'HTMLOptionElement' in window,
          textarea: 'HTMLTextAreaElement' in window,
          select: 'HTMLSelectElement' in window,
          button: 'HTMLButtonElement' in window,
          label: 'HTMLLabelElement' in window
        };
      });

      expect(formElements.datalist).toBe(true);
      expect(formElements.output).toBe(true);
      expect(formElements.progress).toBe(true);
      expect(formElements.meter).toBe(true);
      expect(formElements.details).toBe(true);
      expect(formElements.textarea).toBe(true);
      expect(formElements.select).toBe(true);
      expect(formElements.button).toBe(true);
      expect(formElements.label).toBe(true);
    });
  });

  test.describe('Media and Graphics Features', () => {
    test('should detect media API features', async ({ page }) => {
      const mediaFeatures = await page.evaluate(() => {
        return {
          audio: 'HTMLAudioElement' in window,
          video: 'HTMLVideoElement' in window,
          canvas: 'HTMLCanvasElement' in window,
          webgl: (() => {
            try {
              const canvas = document.createElement('canvas');
              return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
            } catch (e) {
              return false;
            }
          })(),
          webgl2: (() => {
            try {
              const canvas = document.createElement('canvas');
              return !!canvas.getContext('webgl2');
            } catch (e) {
              return false;
            }
          })(),
          svg: 'SVGElement' in window,
          picture: 'HTMLPictureElement' in window,
          track: 'HTMLTrackElement' in window,
          source: 'HTMLSourceElement' in window,
          media_recorder: 'MediaRecorder' in window,
          media_stream: 'MediaStream' in window,
          getUserMedia: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices
        };
      });

      expect(mediaFeatures.audio).toBe(true);
      expect(mediaFeatures.video).toBe(true);
      expect(mediaFeatures.canvas).toBe(true);
      expect(mediaFeatures.svg).toBe(true);
      expect(mediaFeatures.picture).toBe(true);
      expect(mediaFeatures.track).toBe(true);
      expect(mediaFeatures.source).toBe(true);
      expect(mediaFeatures.getUserMedia).toBe(browserCapabilities.mediaDevices);
    });

    test('should detect canvas capabilities', async ({ page }) => {
      const canvasFeatures = await page.evaluate(() => {
        const canvas = document.createElement('canvas');
        const ctx2d = canvas.getContext('2d');
        
        return {
          has2d: !!ctx2d,
          hasWebGL: !!canvas.getContext('webgl'),
          hasWebGL2: !!canvas.getContext('webgl2'),
          imageData: ctx2d && 'createImageData' in ctx2d,
          path2d: 'Path2D' in window,
          offscreenCanvas: 'OffscreenCanvas' in window,
          imageBitmap: 'createImageBitmap' in window,
          canvasFilters: ctx2d && 'filter' in ctx2d,
          canvasText: ctx2d && 'fillText' in ctx2d,
          canvasTransforms: ctx2d && 'setTransform' in ctx2d
        };
      });

      expect(canvasFeatures.has2d).toBe(true);
      expect(canvasFeatures.imageData).toBe(true);
      expect(canvasFeatures.canvasText).toBe(true);
      expect(canvasFeatures.canvasTransforms).toBe(true);
    });
  });

  test.describe('Browser-Specific Feature Detection', () => {
    test('should detect Safari-specific features', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari', 'Safari-specific test');
      
      const safariFeatures = await page.evaluate(() => {
        return {
          webkitPrefixes: 'webkitRequestAnimationFrame' in window,
          touchForceChange: 'ontouchforcechange' in document,
          visualViewport: 'visualViewport' in window,
          paymentRequest: 'PaymentRequest' in window,
          applePaySession: 'ApplePaySession' in window,
          webkitSpeechRecognition: 'webkitSpeechRecognition' in window,
          safariSpecific: /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
        };
      });

      expect(safariFeatures.safariSpecific).toBe(true);
      expect(safariFeatures.visualViewport).toBe(true);
    });

    test('should detect Chrome-specific features', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');
      
      const chromeFeatures = await page.evaluate(() => {
        return {
          chrome: 'chrome' in window,
          webkitPrefixes: 'webkitRequestAnimationFrame' in window,
          chromeSpecific: /Chrome/.test(navigator.userAgent),
          performanceObserver: 'PerformanceObserver' in window,
          intersectionObserver: 'IntersectionObserver' in window,
          resizeObserver: 'ResizeObserver' in window,
          webComponents: 'customElements' in window,
          lazyLoading: 'loading' in document.createElement('img')
        };
      });

      expect(chromeFeatures.chromeSpecific).toBe(true);
      expect(chromeFeatures.performanceObserver).toBe(true);
      expect(chromeFeatures.intersectionObserver).toBe(true);
      expect(chromeFeatures.webComponents).toBe(true);
    });

    test('should detect Firefox-specific features', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');
      
      const firefoxFeatures = await page.evaluate(() => {
        return {
          mozPrefixes: 'mozRequestAnimationFrame' in window,
          firefoxSpecific: /Firefox/.test(navigator.userAgent),
          mozConnection: 'mozConnection' in navigator,
          mozBattery: 'mozBattery' in navigator,
          mozPaintCount: 'mozPaintCount' in window,
          mozInputSource: 'mozInputSource' in MouseEvent.prototype,
          mozImageSmoothingEnabled: (() => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            return ctx && 'mozImageSmoothingEnabled' in ctx;
          })()
        };
      });

      expect(firefoxFeatures.firefoxSpecific).toBe(true);
    });

    test('should detect Edge-specific features', async ({ page }) => {
      test.skip(browserInfo.name !== 'edge', 'Edge-specific test');
      
      const edgeFeatures = await page.evaluate(() => {
        return {
          edge: /Edge/.test(navigator.userAgent) || /Edg/.test(navigator.userAgent),
          msLaunchUri: 'msLaunchUri' in navigator,
          msSaveBlob: 'msSaveBlob' in navigator,
          msPointerEvents: 'msPointerEnabled' in navigator,
          edgeSpecific: /Edge/.test(navigator.userAgent) || /Edg/.test(navigator.userAgent)
        };
      });

      expect(edgeFeatures.edgeSpecific).toBe(true);
    });
  });

  test.describe('Progressive Web App Features', () => {
    test('should detect PWA capabilities', async ({ page }) => {
      const pwaFeatures = await page.evaluate(() => {
        return {
          serviceWorker: 'serviceWorker' in navigator,
          pushManager: 'PushManager' in window,
          notifications: 'Notification' in window,
          backgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype,
          webAppManifest: 'onappinstalled' in window,
          beforeInstallPrompt: 'onbeforeinstallprompt' in window,
          getInstalledRelatedApps: 'getInstalledRelatedApps' in navigator,
          share: 'share' in navigator,
          clipboard: 'clipboard' in navigator,
          permissions: 'permissions' in navigator,
          wakeLock: 'wakeLock' in navigator,
          deviceMemory: 'deviceMemory' in navigator,
          connection: 'connection' in navigator,
          storage: 'storage' in navigator,
          persistent: 'storage' in navigator && 'persist' in navigator.storage
        };
      });

      expect(pwaFeatures.serviceWorker).toBe(browserCapabilities.serviceWorkers);
      expect(pwaFeatures.pushManager).toBe(browserCapabilities.pushNotifications);
      expect(pwaFeatures.notifications).toBe(true);
      expect(pwaFeatures.permissions).toBe(true);
    });

    test('should detect installability features', async ({ page }) => {
      const installFeatures = await page.evaluate(() => {
        return {
          beforeInstallPrompt: 'onbeforeinstallprompt' in window,
          appInstalled: 'onappinstalled' in window,
          relatedApps: 'getInstalledRelatedApps' in navigator,
          webAppManifest: (() => {
            const link = document.createElement('link');
            link.rel = 'manifest';
            return 'rel' in link;
          })(),
          displayMode: 'matchMedia' in window && window.matchMedia('(display-mode: standalone)').matches,
          fullscreen: 'requestFullscreen' in document.documentElement
        };
      });

      expect(installFeatures.webAppManifest).toBe(true);
      expect(installFeatures.fullscreen).toBe(true);
    });
  });

  test.describe('Accessibility Features', () => {
    test('should detect accessibility API support', async ({ page }) => {
      const a11yFeatures = await page.evaluate(() => {
        return {
          ariaAttributes: 'setAttribute' in document.createElement('div'),
          roleAttribute: 'role' in document.createElement('div'),
          tabindex: 'tabIndex' in document.createElement('div'),
          focus: 'focus' in document.createElement('div'),
          blur: 'blur' in document.createElement('div'),
          accessibleName: 'accessibleName' in document.createElement('div'),
          accessibleDescription: 'accessibleDescription' in document.createElement('div'),
          ariaLabel: 'ariaLabel' in document.createElement('div'),
          ariaLabelledBy: 'ariaLabelledBy' in document.createElement('div'),
          ariaDescribedBy: 'ariaDescribedBy' in document.createElement('div'),
          ariaExpanded: 'ariaExpanded' in document.createElement('div'),
          ariaHidden: 'ariaHidden' in document.createElement('div'),
          ariaLive: 'ariaLive' in document.createElement('div')
        };
      });

      expect(a11yFeatures.ariaAttributes).toBe(true);
      expect(a11yFeatures.roleAttribute).toBe(true);
      expect(a11yFeatures.tabindex).toBe(true);
      expect(a11yFeatures.focus).toBe(true);
      expect(a11yFeatures.blur).toBe(true);
    });

    test('should detect screen reader support', async ({ page }) => {
      const screenReaderSupport = await page.evaluate(() => {
        return {
          ariaLive: 'ariaLive' in document.createElement('div'),
          ariaAtomic: 'ariaAtomic' in document.createElement('div'),
          ariaRelevant: 'ariaRelevant' in document.createElement('div'),
          ariaBusy: 'ariaBusy' in document.createElement('div'),
          ariaPolite: true, // Basic support assumption
          ariaAssertive: true, // Basic support assumption
          liveRegions: 'setAttribute' in document.createElement('div')
        };
      });

      expect(screenReaderSupport.ariaLive).toBe(true);
      expect(screenReaderSupport.liveRegions).toBe(true);
    });
  });
});
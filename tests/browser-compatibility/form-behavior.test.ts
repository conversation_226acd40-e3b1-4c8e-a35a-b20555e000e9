import { browserTest as test, expect, BrowserInfo } from '../utils/browser-config';

test.describe('Browser Form Behavior Compatibility', () => {
  let browserInfo: BrowserInfo;

  test.beforeEach(async ({ page, browserInfo: info }) => {
    browserInfo = info;
    await page.goto('/');
  });

  test.describe('Input Field Behavior', () => {
    test('should handle input types consistently', async ({ page }) => {
      const formHtml = `
        <form id="test-form">
          <input type="text" id="text-input" />
          <input type="email" id="email-input" />
          <input type="password" id="password-input" />
          <input type="number" id="number-input" />
          <input type="tel" id="tel-input" />
          <input type="url" id="url-input" />
          <input type="date" id="date-input" />
          <input type="time" id="time-input" />
          <input type="range" id="range-input" min="0" max="100" />
          <input type="color" id="color-input" />
          <input type="file" id="file-input" />
          <input type="checkbox" id="checkbox-input" />
          <input type="radio" id="radio-input" name="radio-group" />
          <select id="select-input">
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </select>
          <textarea id="textarea-input"></textarea>
        </form>
      `;

      await page.setContent(formHtml);

      const inputTypes = await page.evaluate(() => {
        const inputs = document.querySelectorAll('input, select, textarea');
        const results: { [key: string]: string } = {};
        
        inputs.forEach((input) => {
          const element = input as HTMLInputElement;
          results[element.id] = element.type;
        });
        
        return results;
      });

      expect(inputTypes['text-input']).toBe('text');
      expect(inputTypes['email-input']).toBe('email');
      expect(inputTypes['password-input']).toBe('password');
      expect(inputTypes['number-input']).toBe('number');
      
      // Some browsers may fall back to 'text' for unsupported types
      expect(['tel', 'text']).toContain(inputTypes['tel-input']);
      expect(['url', 'text']).toContain(inputTypes['url-input']);
    });

    test('should handle placeholder behavior', async ({ page }) => {
      const formHtml = `
        <form>
          <input type="text" id="placeholder-test" placeholder="Enter text here" />
          <input type="email" id="email-placeholder" placeholder="Enter email" />
          <textarea id="textarea-placeholder" placeholder="Enter message"></textarea>
        </form>
      `;

      await page.setContent(formHtml);

      const placeholderSupport = await page.evaluate(() => {
        const textInput = document.getElementById('placeholder-test') as HTMLInputElement;
        const emailInput = document.getElementById('email-placeholder') as HTMLInputElement;
        const textarea = document.getElementById('textarea-placeholder') as HTMLTextAreaElement;
        
        return {
          textPlaceholder: textInput.placeholder,
          emailPlaceholder: emailInput.placeholder,
          textareaPlaceholder: textarea.placeholder,
          supportsPlaceholder: 'placeholder' in document.createElement('input')
        };
      });

      expect(placeholderSupport.supportsPlaceholder).toBe(true);
      expect(placeholderSupport.textPlaceholder).toBe('Enter text here');
      expect(placeholderSupport.emailPlaceholder).toBe('Enter email');
      expect(placeholderSupport.textareaPlaceholder).toBe('Enter message');
    });

    test('should handle autofocus behavior', async ({ page }) => {
      const formHtml = `
        <form>
          <input type="text" id="first-input" />
          <input type="text" id="autofocus-input" autofocus />
          <input type="text" id="third-input" />
        </form>
      `;

      await page.setContent(formHtml);
      await page.waitForTimeout(100); // Allow time for autofocus

      const focusedElement = await page.evaluate(() => {
        return document.activeElement?.id;
      });

      expect(focusedElement).toBe('autofocus-input');
    });

    test('should handle required attribute validation', async ({ page }) => {
      const formHtml = `
        <form id="validation-form">
          <input type="text" id="required-text" required />
          <input type="email" id="required-email" required />
          <input type="submit" value="Submit" />
        </form>
      `;

      await page.setContent(formHtml);

      const validationResult = await page.evaluate(() => {
        const form = document.getElementById('validation-form') as HTMLFormElement;
        const textInput = document.getElementById('required-text') as HTMLInputElement;
        const emailInput = document.getElementById('required-email') as HTMLInputElement;
        
        return {
          textValid: textInput.checkValidity(),
          emailValid: emailInput.checkValidity(),
          formValid: form.checkValidity(),
          supportsValidation: 'checkValidity' in textInput
        };
      });

      expect(validationResult.supportsValidation).toBe(true);
      expect(validationResult.textValid).toBe(false);
      expect(validationResult.emailValid).toBe(false);
      expect(validationResult.formValid).toBe(false);
    });
  });

  test.describe('Form Submission Behavior', () => {
    test('should handle form submission methods', async ({ page }) => {
      const formHtml = `
        <form id="get-form" method="GET" action="/test">
          <input type="text" name="get-param" value="get-value" />
          <input type="submit" id="get-submit" value="GET Submit" />
        </form>
        <form id="post-form" method="POST" action="/test">
          <input type="text" name="post-param" value="post-value" />
          <input type="submit" id="post-submit" value="POST Submit" />
        </form>
      `;

      await page.setContent(formHtml);

      const formMethods = await page.evaluate(() => {
        const getForm = document.getElementById('get-form') as HTMLFormElement;
        const postForm = document.getElementById('post-form') as HTMLFormElement;
        
        return {
          getMethod: getForm.method,
          postMethod: postForm.method,
          getAction: getForm.action,
          postAction: postForm.action
        };
      });

      expect(formMethods.getMethod.toLowerCase()).toBe('get');
      expect(formMethods.postMethod.toLowerCase()).toBe('post');
      expect(formMethods.getAction).toContain('/test');
      expect(formMethods.postAction).toContain('/test');
    });

    test('should handle formdata creation', async ({ page }) => {
      const formHtml = `
        <form id="formdata-test">
          <input type="text" name="text-field" value="text-value" />
          <input type="email" name="email-field" value="<EMAIL>" />
          <input type="hidden" name="hidden-field" value="hidden-value" />
          <input type="checkbox" name="checkbox-field" value="checkbox-value" checked />
          <select name="select-field">
            <option value="option1" selected>Option 1</option>
            <option value="option2">Option 2</option>
          </select>
          <textarea name="textarea-field">textarea content</textarea>
        </form>
      `;

      await page.setContent(formHtml);

      const formDataResult = await page.evaluate(() => {
        const form = document.getElementById('formdata-test') as HTMLFormElement;
        const formData = new FormData(form);
        
        const data: { [key: string]: string } = {};
        formData.forEach((value, key) => {
          data[key] = value.toString();
        });
        
        return {
          data,
          supportsFormData: typeof FormData !== 'undefined'
        };
      });

      expect(formDataResult.supportsFormData).toBe(true);
      expect(formDataResult.data['text-field']).toBe('text-value');
      expect(formDataResult.data['email-field']).toBe('<EMAIL>');
      expect(formDataResult.data['hidden-field']).toBe('hidden-value');
      expect(formDataResult.data['checkbox-field']).toBe('checkbox-value');
      expect(formDataResult.data['select-field']).toBe('option1');
      expect(formDataResult.data['textarea-field']).toBe('textarea content');
    });
  });

  test.describe('Input Event Handling', () => {
    test('should handle input event types consistently', async ({ page }) => {
      const formHtml = `
        <form>
          <input type="text" id="event-test-input" />
        </form>
      `;

      await page.setContent(formHtml);

      const eventResults = await page.evaluate(() => {
        const input = document.getElementById('event-test-input') as HTMLInputElement;
        const events: string[] = [];
        
        const eventTypes = ['input', 'change', 'focus', 'blur', 'keydown', 'keyup', 'keypress'];
        eventTypes.forEach(eventType => {
          input.addEventListener(eventType, () => {
            events.push(eventType);
          });
        });
        
        // Simulate user input
        input.focus();
        input.value = 'test';
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.blur();
        
        return events;
      });

      expect(eventResults).toContain('focus');
      expect(eventResults).toContain('input');
      expect(eventResults).toContain('change');
      expect(eventResults).toContain('blur');
    });

    test('should handle paste events', async ({ page }) => {
      const formHtml = `
        <form>
          <input type="text" id="paste-test-input" />
        </form>
      `;

      await page.setContent(formHtml);

      await page.focus('#paste-test-input');

      const pasteResult = await page.evaluate(() => {
        const input = document.getElementById('paste-test-input') as HTMLInputElement;
        let pasteEventFired = false;
        
        input.addEventListener('paste', (e) => {
          pasteEventFired = true;
        });
        
        // Simulate paste event
        const pasteEvent = new ClipboardEvent('paste', {
          bubbles: true,
          cancelable: true,
          clipboardData: new DataTransfer()
        });
        
        input.dispatchEvent(pasteEvent);
        
        return pasteEventFired;
      });

      expect(pasteResult).toBe(true);
    });
  });

  test.describe('Browser-Specific Form Quirks', () => {
    test('should handle Safari date input format', async ({ page }) => {
      test.skip(browserInfo.name !== 'safari', 'Safari-specific test');
      
      const formHtml = `
        <form>
          <input type="date" id="safari-date-input" />
        </form>
      `;

      await page.setContent(formHtml);

      const dateInputType = await page.evaluate(() => {
        const input = document.getElementById('safari-date-input') as HTMLInputElement;
        return input.type;
      });

      expect(dateInputType).toBe('date');
    });

    test('should handle Firefox input number step behavior', async ({ page }) => {
      test.skip(browserInfo.name !== 'firefox', 'Firefox-specific test');
      
      const formHtml = `
        <form>
          <input type="number" id="firefox-number-input" step="0.01" />
        </form>
      `;

      await page.setContent(formHtml);

      const stepValue = await page.evaluate(() => {
        const input = document.getElementById('firefox-number-input') as HTMLInputElement;
        return input.step;
      });

      expect(stepValue).toBe('0.01');
    });

    test('should handle Chrome autofill behavior', async ({ page }) => {
      test.skip(browserInfo.name !== 'chrome', 'Chrome-specific test');
      
      const formHtml = `
        <form>
          <input type="email" id="chrome-email-input" autocomplete="email" />
          <input type="password" id="chrome-password-input" autocomplete="current-password" />
        </form>
      `;

      await page.setContent(formHtml);

      const autocompleteValues = await page.evaluate(() => {
        const emailInput = document.getElementById('chrome-email-input') as HTMLInputElement;
        const passwordInput = document.getElementById('chrome-password-input') as HTMLInputElement;
        
        return {
          emailAutocomplete: emailInput.autocomplete,
          passwordAutocomplete: passwordInput.autocomplete
        };
      });

      expect(autocompleteValues.emailAutocomplete).toBe('email');
      expect(autocompleteValues.passwordAutocomplete).toBe('current-password');
    });
  });

  test.describe('Mobile Form Behavior', () => {
    test('should handle touch events on mobile', async ({ page }) => {
      test.skip(!browserInfo.isMobile, 'Mobile-specific test');
      
      const formHtml = `
        <form>
          <input type="text" id="mobile-touch-input" />
        </form>
      `;

      await page.setContent(formHtml);

      const touchSupport = await page.evaluate(() => {
        return {
          supportsTouch: 'ontouchstart' in window,
          supportsTouchEvents: 'TouchEvent' in window
        };
      });

      expect(touchSupport.supportsTouch).toBe(true);
    });

    test('should handle mobile keyboard types', async ({ page }) => {
      test.skip(!browserInfo.isMobile, 'Mobile-specific test');
      
      const formHtml = `
        <form>
          <input type="tel" id="mobile-tel-input" />
          <input type="email" id="mobile-email-input" />
          <input type="number" id="mobile-number-input" />
          <input type="url" id="mobile-url-input" />
        </form>
      `;

      await page.setContent(formHtml);

      const inputTypes = await page.evaluate(() => {
        const telInput = document.getElementById('mobile-tel-input') as HTMLInputElement;
        const emailInput = document.getElementById('mobile-email-input') as HTMLInputElement;
        const numberInput = document.getElementById('mobile-number-input') as HTMLInputElement;
        const urlInput = document.getElementById('mobile-url-input') as HTMLInputElement;
        
        return {
          tel: telInput.type,
          email: emailInput.type,
          number: numberInput.type,
          url: urlInput.type
        };
      });

      expect(['tel', 'text']).toContain(inputTypes.tel);
      expect(inputTypes.email).toBe('email');
      expect(inputTypes.number).toBe('number');
      expect(['url', 'text']).toContain(inputTypes.url);
    });
  });

  test.describe('Accessibility Form Features', () => {
    test('should handle ARIA attributes', async ({ page }) => {
      const formHtml = `
        <form>
          <label for="aria-input">Input with ARIA:</label>
          <input 
            type="text" 
            id="aria-input" 
            aria-describedby="help-text"
            aria-required="true"
            aria-invalid="false"
          />
          <div id="help-text">This is help text</div>
        </form>
      `;

      await page.setContent(formHtml);

      const ariaAttributes = await page.evaluate(() => {
        const input = document.getElementById('aria-input') as HTMLInputElement;
        
        return {
          ariaDescribedBy: input.getAttribute('aria-describedby'),
          ariaRequired: input.getAttribute('aria-required'),
          ariaInvalid: input.getAttribute('aria-invalid'),
          hasLabel: !!document.querySelector('label[for="aria-input"]')
        };
      });

      expect(ariaAttributes.ariaDescribedBy).toBe('help-text');
      expect(ariaAttributes.ariaRequired).toBe('true');
      expect(ariaAttributes.ariaInvalid).toBe('false');
      expect(ariaAttributes.hasLabel).toBe(true);
    });

    test('should handle keyboard navigation', async ({ page }) => {
      const formHtml = `
        <form>
          <input type="text" id="input1" />
          <input type="text" id="input2" />
          <input type="text" id="input3" />
          <button type="submit" id="submit-btn">Submit</button>
        </form>
      `;

      await page.setContent(formHtml);

      await page.focus('#input1');
      await page.keyboard.press('Tab');
      let focusedElement = await page.evaluate(() => document.activeElement?.id);
      expect(focusedElement).toBe('input2');

      await page.keyboard.press('Tab');
      focusedElement = await page.evaluate(() => document.activeElement?.id);
      expect(focusedElement).toBe('input3');

      await page.keyboard.press('Tab');
      focusedElement = await page.evaluate(() => document.activeElement?.id);
      expect(focusedElement).toBe('submit-btn');
    });
  });
});
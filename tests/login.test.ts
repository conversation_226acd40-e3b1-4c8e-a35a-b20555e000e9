import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test('should display login page elements', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Salmate/);

    // Check for login form presence
    await expect(page.locator('form')).toBeVisible();

    // Check for username input
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('label[for="username"]')).toContainText('Username');

    // Check for password input
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('label[for="password"]')).toContainText('Password');

    // Check for submit button
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toContainText('Login');
  });

  test('should show validation errors for empty fields', async ({ page }) => {
    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check for username validation error
    await expect(page.locator('text=Username is required')).toBeVisible();

    // Check for password validation error
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should validate username format', async ({ page }) => {
    // Test invalid username characters
    await page.fill('input[name="username"]', 'invalid@user');
    await page.fill('input[name="password"]', 'password123');

    // The input should filter out invalid characters
    const usernameValue = await page.locator('input[name="username"]').inputValue();
    expect(usernameValue).toBe('invaliduser');
  });

  test('should handle successful login', async ({ page }) => {
    // Fill in valid credentials
    await page.fill('input[name="username"]', 'testuser1');
    await page.fill('input[name="password"]', 'Testpass@1');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to home page on successful login
    await expect(page).toHaveURL('/chat_center');
  });

  test('should handle login failure', async ({ page }) => {
    // Fill in invalid credentials
    await page.fill('input[name="username"]', 'wronguser');
    await page.fill('input[name="password"]', 'wrongpass');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show error message
    await expect(page.locator('text=Invalid username or password')).toBeVisible();

    // Should remain on login page
    await expect(page).toHaveURL('/login');
  });

  test('should show loading state during login', async ({ page }) => {
    // Mock slow API response
    await page.route('**/user/login/', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    // Fill in credentials
    await page.fill('input[name="username"]', 'testuser1');
    await page.fill('input[name="password"]', 'Testpass@1');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show loading spinner
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
  });

  test('should have accessible form elements', async ({ page }) => {
    // Check that form inputs have proper labels
    await expect(page.locator('label[for="username"]')).toBeVisible();
    await expect(page.locator('label[for="password"]')).toBeVisible();

    // Check that inputs are properly connected to labels
    const usernameInput = page.locator('input[name="username"]');
    const passwordInput = page.locator('input[name="password"]');

    await expect(usernameInput).toHaveAttribute('id', 'username');
    await expect(passwordInput).toHaveAttribute('id', 'password');
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Start by focusing on the first input explicitly
    await page.locator('input[name="username"]').focus();
    await expect(page.locator('input[name="username"]')).toBeFocused();

    // Tab to password field
    await page.keyboard.press('Tab');
    await expect(page.locator('input[name="password"]')).toBeFocused();

    // Tab to submit button
    await page.keyboard.press('Tab');
    await expect(page.locator('button[type="submit"]')).toBeFocused();
  });
});
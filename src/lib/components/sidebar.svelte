<script lang="ts">
	import { onMount } from 'svelte';
	import { goto, invalidateAll } from '$app/navigation';
	import { page } from '$app/stores';
	import {
		Sidebar,
		SidebarWrapper,
		SidebarBrand,
		SidebarItem,
		SidebarGroup,
		SidebarDropdownWrapper,
		SidebarDropdownItem,
		Avatar,
		Button,
		Tooltip
	} from 'flowbite-svelte';

	// Import only the icons that your package definitely has
	import {
		ChevronDownOutline,
		ChevronUpOutline,
		ChevronRightOutline,
		ChevronLeftOutline
	} from 'flowbite-svelte-icons';

	import LanguageToggle from './LanguageToggle.svelte';
	import { t } from '$lib/stores/i18n';
	import { createLanguageRenderer } from '$lib/utils/languageUtils';

	export let isLogin: boolean;
	export let id: number;
	export let name_avartar: string;
	export let fullname: string;
	export let email: string;
	export let status: string;
	export let role: string;

	// Language preference management
	const { isReady: isLanguageReady, language: currentLanguage } = createLanguageRenderer();

	// Navbar styling state
	let dominantColor = '#ffffff';
	let secondaryColor = '#ffffff';
	let accentColor = '#ffffff';
	let logoCompany = '';
	let companyName = t('loading');

	// Sidebar state
	let isMinimized = false;
	// Track previous minimized state for dropdown toggle
	let wasMinimized = false;

	function toggleSidebar() {
		isMinimized = !isMinimized;
		// whenever we toggle…
		if (isMinimized) {
			clearSidebarAutoMinimize();
		} else {
			scheduleSidebarAutoMinimize();
		}
	}

	// Slide-up menu state
	let isMenuOpen = false;
	// Animation states
	let menuAnimationState = 'closed'; // 'closed', 'opening', 'open', 'closing'
	// Auto-minimize on inactivity
	let sidebarAutoMinTimeout: ReturnType<typeof setTimeout>;

	// schedule a 10s auto-minimize when sidebar is open
	function scheduleSidebarAutoMinimize() {
		clearTimeout(sidebarAutoMinTimeout);
		if (!isMinimized) {
			sidebarAutoMinTimeout = setTimeout(() => {
				if (!isMinimized) toggleSidebar();
			}, 10_000);
		}
	}

	// clear whenever we collapse or on any user interaction
	function clearSidebarAutoMinimize() {
		clearTimeout(sidebarAutoMinTimeout);
	}

	// wire up global activity listeners
	onMount(() => {
		const reset = () => scheduleSidebarAutoMinimize();
		document.addEventListener('mousemove', reset);
		document.addEventListener('keydown', reset);
		document.addEventListener('click', reset);

		// if starting expanded, kick off the timer
		scheduleSidebarAutoMinimize();

		return () => {
			document.removeEventListener('mousemove', reset);
			document.removeEventListener('keydown', reset);
			document.removeEventListener('click', reset);
			clearSidebarAutoMinimize();
		};
	});

	// auto-close timer handle
	let autoCloseTimeout: ReturnType<typeof setTimeout>;

	function toggleMenu() {
		if (isMenuOpen) {
			clearTimeout(autoCloseTimeout);
			// Start closing animation
			menuAnimationState = 'closing';
			// After animation completes, set the menu to closed
			setTimeout(() => {
				isMenuOpen = false;
				menuAnimationState = 'closed';
			}, 300); // Match this with the CSS animation duration
		} else {
			isMenuOpen = true;
			menuAnimationState = 'opening';
			isMinimized = false; // Always expand sidebar when opening menu
			// After animation completes, set the state to open
			setTimeout(() => {
				menuAnimationState = 'open';
			}, 300);

			// schedule auto-close in 10s if still open
			autoCloseTimeout = setTimeout(() => {
				if (isMenuOpen) toggleMenu();
			}, 5_000);
		}
	}

	// Close menu when clicking outside
	function handleClickOutside(event: MouseEvent) {
		const menu = document.getElementById('slide-up-menu');
		const avatar = document.getElementById('avatar-trigger');
		const target = event.target as Node | null;

		if (
			isMenuOpen &&
			menu &&
			avatar &&
			target &&
			!menu.contains(target) &&
			!avatar.contains(target)
		) {
			clearTimeout(autoCloseTimeout);
			toggleMenu(); // Use the toggle function to get animation effect
		}
	}

	// Cache expiration check
	function isCacheExpired(timestamp: number) {
		// Cache expires after 1 hour (3600000 ms)
		const expirationTime = 3600000;
		return Date.now() - timestamp > expirationTime;
	}

	onMount(() => {
		// Add event listener for closing menu
		document.addEventListener('click', handleClickOutside);

		// Load settings
		const cached = localStorage.getItem('app_settings');
		if (cached) {
			try {
				const cachedData = JSON.parse(cached);

				// Check if cache is expired or if dominantColor has changed
				if (isCacheExpired(cachedData.ts)) {
					console.log('sidebar.svelte: isCacheExpired(): Cache expired, fetching fresh settings');
					fetchSettings(true);
				} else {
					// Apply cached settings
					console.log('sidebar.svelte: isCacheExpired(): Using cached settings');
					applySettings(cachedData.data);

					// Still fetch in background to check for updates
					fetchSettings(false);
				}
			} catch (err) {
				console.error('sidebar.svelte: isCacheExpired(): Error parsing cached settings:', err);
				fetchSettings(true);
			}
		} else {
			fetchSettings(true);
		}

		// Clean up event listener on component destroy
		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});

	function applySettings(s: any) {
		// Store old values to check for changes
		const oldDominantColor = dominantColor;

		// Apply new settings
		dominantColor = s.DOMINANT_COLOR;
		secondaryColor = s.SECONDARY_COLOR;
		accentColor = s.ACCENT_COLOR;
		logoCompany = s.COMPANY_LOGO;
		companyName = s.COMPANY_ENGLISH_NAME;
	}

	async function fetchSettings(forceUpdate: boolean = false): Promise<void> {
		try {
			// Add cache-busting parameter if forcing an update
			const queryParam = forceUpdate ? `?t=${Date.now()}` : '';
			const res = await fetch(`/api/settings/${queryParam}`);
			const json = await res.json();
			const s = json.system_settings;

			// Check if the settings are different from what we have
			let hasChanges = false;
			if (
				s.DOMINANT_COLOR !== dominantColor ||
				s.SECONDARY_COLOR !== secondaryColor ||
				s.ACCENT_COLOR !== accentColor ||
				s.COMPANY_LOGO !== logoCompany ||
				s.COMPANY_ENGLISH_NAME !== companyName
			) {
				hasChanges = true;
			}

			if (hasChanges || forceUpdate) {
				console.log('sidebar.svelte: fetchSettings(): Applying new settings from API');
				applySettings(s);

				// Update cache with new settings
				localStorage.setItem('app_settings', JSON.stringify({ data: s, ts: Date.now() }));
			} else {
				console.log('sidebar.svelte: fetchSettings(): No changes detected in settings');
			}
		} catch (err) {
			console.error('sidebar.svelte: fetchSettings(): Error fetching settings:', err);
		}
	}

	// Status logic
	let userStatus = status;
	// const statusOptions = [
	//   { id: 'online', label: 'Online', color: 'text-green-500', dotColor: 'bg-green-500' },
	//   { id: 'away',   label: 'Away',   color: 'text-yellow-500', dotColor: 'bg-yellow-500' }
	// ];
	$: statusOptions = [
		{ id: 'online', label: t('online'), dotColor: 'bg-green-500' },
		{ id: 'away', label: t('away'), dotColor: 'bg-yellow-500' }
	];

	async function setStatus(s: string) {
		try {
			const res = await fetch(`/api/users/update_my_status?status=${s}`);
			const result = await res.json();
			if (result.message === 'ok') {
				userStatus = result.new_status;
				toggleMenu(); // Close with animation
			} else {
				alert('Changing Status failed. Please try again.');
			}
		} catch {
			alert('Changing Status failed. Please try again.');
		}
	}

	// Logout
	async function logout() {
		try {
			const res = await fetch('/api/logout');
			const result = await res.json();
			if (result.message === 'ok') {
				// Clear language preference from localStorage
				if (result.clearLanguage) {
					localStorage.removeItem('lang');
				}
				window.location.href = '/';
			} else {
				alert('Logout failed. Please try again.');
			}
		} catch {
			alert('Logout failed. Please try again.');
		}
	}

	// Nav helpers
	// $: activeUrl = $page.url.pathname.split('/', 2).join('/');
	$: activeUrl = $page.url.pathname.split('/').filter(Boolean).join('/');

	// For Debugging
	$: console.info('sidebar.svelte: Variables: activeUrl: ' + activeUrl);

	let spanClass = 'flex-1 ms-3 whitespace-nowrap';

	// Only use the reactive declaration for site
	$: site = {
		name: companyName,
		href: '/',
		img: logoCompany
	};

	// Settings dropdown state
	let isSettingsOpen = false;

	function toggleSettings() {
		// If sidebar is minimized, expand it first and then open the dropdown
		if (isMinimized) {
			wasMinimized = true;
			isMinimized = false;
			// Set timeout to ensure sidebar expands first
			setTimeout(() => {
				isSettingsOpen = !isSettingsOpen;
			}, 10);
		} else {
			// Regular toggle if sidebar is already expanded
			isSettingsOpen = !isSettingsOpen;
		}
	}

	// Knowledge Base dropdown state
	let isKnowledgeBaseOpen = false;

	function toggleKnowledgeBase() {
		// If sidebar is minimized, expand it first and then open the dropdown
		if (isMinimized) {
			wasMinimized = true;
			isMinimized = false;
			// Set timeout to ensure sidebar expands first
			setTimeout(() => {
				isKnowledgeBaseOpen = !isKnowledgeBaseOpen;
			}, 10);
		} else {
			// Regular toggle if sidebar is already expanded
			isKnowledgeBaseOpen = !isKnowledgeBaseOpen;
		}
	}

	// Handle clicking outside dropdowns to close them and restore minimized state
	function handleClickOutsideDropdowns(event: MouseEvent) {
		const knowledgeBaseTrigger = document.getElementById('menu-item-knowledge-base');
		const settingsTrigger = document.getElementById('menu-item-settings');
		const target = event.target as Node | null;

		// Check if click is outside both triggers
		if (
			(isKnowledgeBaseOpen || isSettingsOpen) &&
			wasMinimized &&
			target &&
			!knowledgeBaseTrigger?.contains(target) &&
			!settingsTrigger?.contains(target)
		) {
			// Close the dropdowns and restore minimized state
			isKnowledgeBaseOpen = false;
			isSettingsOpen = false;

			// Set a small timeout to make the transition smoother
			setTimeout(() => {
				isMinimized = true;
				wasMinimized = false;
			}, 200);
		}
	}

	// Add event listener for handling outside clicks on dropdowns
	onMount(() => {
		document.addEventListener('click', handleClickOutsideDropdowns);

		return () => {
			document.removeEventListener('click', handleClickOutsideDropdowns);
		};
	});

	// Menu items - arranged in the specified order with Testing moved to Knowledge Base dropdown
	const menuItems = [
		// {
		//     label: 'Home',
		//     key: 'home',
		//     href: '/',
		//     icon: '/navbar/home.svg',
		//     activeCheck: '/home'
		// },
		{
			label: 'Chat_Center',
			key: 'chat_center',
			href: '/chat_center',
			icon: '/navbar/chat.svg',
			activeCheck: 'chat_center'
		},
		{
			label: 'Dashboard',
			key: 'dashboard',
			href: '/dashboard',
			icon: '/navbar/dashboard.svg',
			activeCheck: 'dashboard'
		},
		{
			label: 'Tickets',
			key: 'tickets',
			href: '/monitoring',
			icon: '/navbar/ticket.svg',
			activeCheck: 'monitoring'
		},
		{
			label: 'Users',
			key: 'users',
			href: '/users',
			icon: '/navbar/users.svg',
			activeCheck: 'users'
		},
		{
			label: 'Customers',
			key: 'customers',
			href: '/customer',
			icon: '/navbar/customers.svg',
			activeCheck: 'customer'
		}
	];

	// Knowledge Base submenu items
	const knowledgeBaseItems = [
		{
			label: 'Upload Files',
			key: 'uploadFiles',
			href: '/knowledge',
			icon: '/navbar/upload-files.svg',
			activeCheck: 'knowledge'
		},
		{
			label: 'Testing',
			key: 'testing',
			href: '/llm_testing',
			icon: '/navbar/testing.svg',
			activeCheck: 'llm_testing'
		}
	];

	// Settings submenu items
	const settingsItems = [
		{
			label: 'Business',
			key: 'business',
			href: '/settings/business',
			icon: '/navbar/general.svg',
			activeCheck: 'settings/business',
			roles: ['Admin', 'Supervisor']
		},
		{
			label: 'Team Management',
			key: 'teamManagement',
			href: '/settings/team',
			icon: '/navbar/team-management.svg',
			activeCheck: 'settings/team',
			roles: ['Admin', 'Supervisor']
		},
		{
			label: 'Account',
			key: 'account',
			href: '/settings/account',
			icon: '/navbar/personal.svg',
			activeCheck: 'settings/account',
			roles: ['Admin', 'Supervisor', 'Agent']
		}
	];

	// Does the current role have anything to show in Settings?
	$: hasSettings = settingsItems.some((i) => !i.roles || i.roles.includes(role));

	// New function for tasks
	function navigateToTasks() {
		goto(`/users/${id}`);
		toggleMenu(); // Close with animation
	}

	function getIconUrl(path: string) {
		return `url(${path})`;
	}
</script>

<div
	class={`flex h-screen flex-col transition-all duration-300 ${isMinimized ? 'w-20' : 'w-56'}`}
	style="background-color: {dominantColor};"
>
	<!-- Logo Section -->
	<div class="flex items-center justify-between p-4">
		{#if !isMinimized}
			<div class="mr-2 flex min-w-0 flex-1 items-center">
				{#if site.img !== ''}
					<img src={site.img} class="mr-3 h-8 flex-shrink-0" alt="Company Logo" />
				{/if}
				<span class="max-w-[calc(100%-36px)] truncate font-semibold text-white">{site.name}</span>
			</div>
		{:else}
			<img src={site.img} class="mx-auto h-8" alt="Company Logo" />
		{/if}
		<button
			class="flex-shrink-0 cursor-pointer rounded p-1 text-white hover:bg-white hover:bg-opacity-20"
			on:click={toggleSidebar}
		>
			{#if isMinimized}
				<ChevronRightOutline class="h-5 w-5" />
			{:else}
				<ChevronLeftOutline class="h-5 w-5" />
			{/if}
		</button>
	</div>

	<!-- Wait for language preference to load before rendering menu items -->
	{#if $isLanguageReady}
		<!-- Menu Items -->
		<div class="flex-1 overflow-y-auto py-4">
			<ul>
				{#each menuItems as item}
					<!-- {#if !item.roles || item.roles.includes(role)} -->
					<li>
						<!-- Regular menu item with tooltip for minimized mode -->
						<a
							href={item.href}
							id={`menu-item-${item.label.toLowerCase().replace(/\s+/g, '-')}`}
							class={`flex items-center p-3 ${isMinimized ? 'justify-center' : 'px-4'} 
                            ${activeUrl === item.activeCheck ? 'relative bg-white bg-opacity-25' : 'hover:bg-white hover:bg-opacity-10'} 
                            cursor-pointer transition-all duration-200`}
						>
							<!-- Active indicator bar -->
							{#if activeUrl === item.activeCheck}
								<div
									class="absolute bottom-0 left-0 top-0 w-1"
									style="background-color: {secondaryColor};"
								></div>
							{/if}
							<span
								class={`flex h-6 w-6 items-center justify-center text-2xl ${activeUrl === item.activeCheck ? 'text-white' : 'text-gray-200'}`}
							>
								<div
									class="nav-icon h-7 w-7"
									style="--icon-url: {getIconUrl(item.icon)}; --icon-color: {accentColor}"
								></div>
							</span>
							{#if !isMinimized}
								<span
									class={`ml-3 ${activeUrl === item.activeCheck ? 'font-bold text-white' : 'text-gray-200'} truncate`}
								>
									<!-- {item.label} -->
									{t(item.key)}
								</span>
							{/if}
						</a>

						<!-- Tooltip for minimized mode -->
						{#if isMinimized}
							<Tooltip
								triggeredBy={`#menu-item-${item.label.toLowerCase().replace(/\s+/g, '-')}`}
								placement="right"
								class="z-50"
							>
								<!-- {item.label} -->
								{t(item.key)}
							</Tooltip>
						{/if}
					</li>
					<!-- {/if} -->
				{/each}

				<!-- Knowledge Base menu with dropdown -->
				<li>
					<!-- Knowledge Base dropdown trigger -->
					<!-- svelte-ignore a11y-no-static-element-interactions -->
					<!-- svelte-ignore a11y-click-events-have-key-events -->
					<div
						id="menu-item-knowledge-base"
						on:click={toggleKnowledgeBase}
						class={`flex items-center p-3 ${isMinimized ? 'justify-center' : 'px-4'} 
                 ${activeUrl === 'knowledge' || activeUrl === 'llm_testing' ? 'relative bg-white bg-opacity-25' : 'hover:bg-white hover:bg-opacity-10'} 
                 cursor-pointer transition-all duration-200`}
					>
						<!-- Active indicator bar -->
						{#if activeUrl === '/knowledge' || activeUrl === '/llm_testing'}
							<div
								class="absolute bottom-0 left-0 top-0 w-1"
								style="background-color: {secondaryColor};"
							></div>
						{/if}
						<span
							class={`flex h-6 w-6 items-center justify-center text-2xl ${activeUrl === 'knowledge' || activeUrl === 'llm_testing' ? 'text-white' : 'text-gray-200'}`}
						>
							<div
								class="nav-icon h-7 w-7"
								style="--icon-url: {getIconUrl(
									'/navbar/knowledge-base.svg'
								)}; --icon-color: {accentColor}"
							></div>
						</span>
						{#if !isMinimized}
							<span
								class={`ml-3 ${activeUrl === 'knowledge' || activeUrl === 'llm_testing' ? 'font-bold text-white' : 'text-gray-200'} truncate`}
							>
								{t('knowledgeBase')}
							</span>
							<span class="ml-auto">
								{#if isKnowledgeBaseOpen}
									<ChevronUpOutline class="h-4 w-4 text-white" />
								{:else}
									<ChevronDownOutline class="h-4 w-4 text-white" />
								{/if}
							</span>
						{/if}
					</div>

					<!-- Tooltip for minimized mode -->
					{#if isMinimized}
						<Tooltip triggeredBy="#menu-item-knowledge-base" placement="right" class="z-50">
							{t('knowledgeBase')}
						</Tooltip>
					{/if}

					<!-- Knowledge Base dropdown items -->
					{#if isKnowledgeBaseOpen && !isMinimized}
						<ul class="space-y-1 py-1">
							{#each knowledgeBaseItems as subItem}
								<li>
									<a
										href={subItem.href}
										class={`flex items-center py-2 pl-9 pr-4
                          ${activeUrl === subItem.activeCheck ? 'relative bg-white bg-opacity-25' : 'hover:bg-white hover:bg-opacity-10'} 
                          cursor-pointer transition-all duration-200`}
									>
										<!-- Active indicator bar -->
										{#if activeUrl === subItem.activeCheck}
											<div
												class="absolute bottom-0 left-0 top-0 w-1"
												style="background-color: {secondaryColor};"
											></div>
										{/if}
										<span
											class={`flex h-5 w-5 items-center justify-center text-lg ${activeUrl === subItem.activeCheck ? 'text-white' : 'text-gray-200'}`}
										>
											<div
												class="nav-icon h-7 w-7"
												style="--icon-url: {getIconUrl(subItem.icon)}; --icon-color: {accentColor}"
											></div>
										</span>
										<span
											class={`ml-2 ${activeUrl === subItem.activeCheck ? 'font-bold text-white' : 'text-gray-200'} truncate`}
										>
											<!-- {subItem.label} -->
											{t(subItem.key)}
										</span>
									</a>
								</li>
							{/each}
						</ul>
					{/if}
				</li>

				<!-- Settings menu with dropdown -->
				<!-- {#if !role || role === 'Admin'} -->
				{#if hasSettings}
					<li>
						<!-- Settings dropdown trigger -->
						<!-- svelte-ignore a11y-click-events-have-key-events -->
						<!-- svelte-ignore a11y-no-static-element-interactions -->
						<div
							id="menu-item-settings"
							on:click={toggleSettings}
							class={`flex items-center p-3 ${isMinimized ? 'justify-center' : 'px-4'} 
                   ${activeUrl.includes('settings/') ? 'relative bg-white bg-opacity-25' : 'hover:bg-white hover:bg-opacity-10'} 
                   cursor-pointer transition-all duration-200`}
						>
							<!-- Active indicator bar -->
							{#if activeUrl.includes('settings/')}
								<div
									class="absolute bottom-0 left-0 top-0 w-1"
									style="background-color: {secondaryColor};"
								></div>
							{/if}
							<span
								class={`flex h-6 w-6 items-center justify-center text-2xl ${activeUrl.includes('settings/') ? 'text-white' : 'text-gray-200'}`}
							>
								<div
									class="nav-icon h-7 w-7"
									style="--icon-url: {getIconUrl(
										'/navbar/settings.svg'
									)}; --icon-color: {accentColor}"
								></div>
							</span>
							{#if !isMinimized}
								<span
									class={`ml-3 ${activeUrl.includes('settings/') ? 'font-bold text-white' : 'text-gray-200'} truncate`}
								>
									{t('settings')}
								</span>
								<span class="ml-auto">
									{#if isSettingsOpen}
										<ChevronUpOutline class="h-4 w-4 text-white" />
									{:else}
										<ChevronDownOutline class="h-4 w-4 text-white" />
									{/if}
								</span>
							{/if}
						</div>

						<!-- Tooltip for minimized mode -->
						{#if isMinimized}
							<Tooltip triggeredBy="#menu-item-settings" placement="right" class="z-50">
								<!-- Settings -->
								{t('settings')}
							</Tooltip>
						{/if}

						<!-- Settings dropdown items -->
						{#if isSettingsOpen && !isMinimized}
							<ul class="space-y-1 py-1">
								{#each settingsItems as subItem}
									{#if !subItem.roles || subItem.roles.includes(role)}
										<li>
											<a
												href={subItem.href}
												class={`flex items-center py-2 pl-9 pr-4
                                        ${activeUrl === subItem.activeCheck ? 'relative bg-white bg-opacity-25' : 'hover:bg-white hover:bg-opacity-10'} 
                                        cursor-pointer transition-all duration-200`}
											>
												<!-- Active indicator bar -->
												{#if activeUrl === subItem.activeCheck}
													<div
														class="absolute bottom-0 left-0 top-0 w-1"
														style="background-color: {secondaryColor};"
													></div>
												{/if}
												<span
													class={`flex h-5 w-5 items-center justify-center text-lg ${activeUrl === subItem.activeCheck ? 'text-white' : 'text-gray-200'}`}
												>
													<div
														class="nav-icon h-7 w-7"
														style="--icon-url: {getIconUrl(
															subItem.icon
														)}; --icon-color: {accentColor}"
													></div>
												</span>
												<span
													class={`ml-2 ${activeUrl === subItem.activeCheck ? 'font-bold text-white' : 'text-gray-200'} truncate`}
												>
													<!-- {subItem.label} -->
													{t(subItem.key)}
												</span>
											</a>
										</li>
									{/if}
								{/each}
							</ul>
						{/if}
					</li>
				{/if}
			</ul>
		</div>

		<!-- User Profile with Slide-up Menu -->
		{#if isLogin}
			<div class="relative">
				<div class={`border-t border-gray-700 p-4 ${isMinimized ? 'flex justify-center' : ''}`}>
					<!-- svelte-ignore a11y-click-events-have-key-events -->
					<!-- svelte-ignore a11y-no-static-element-interactions -->
					<div
						id="avatar-trigger"
						on:click|stopPropagation={toggleMenu}
						class={`${!isMinimized ? 'flex cursor-pointer items-center rounded p-2 transition-all hover:bg-white hover:bg-opacity-10' : 'cursor-pointer rounded-full p-2 transition-all hover:bg-white hover:bg-opacity-10'}`}
					>
						<Avatar>{name_avartar}</Avatar>
						{#if !isMinimized}
							<div class="ms-3 min-w-0 flex-1">
								<p class="truncate text-sm font-medium text-white">{fullname}</p>
								<div class="flex items-center">
									<span
										class={`h-2 w-2 rounded-full ${statusOptions.find((o) => o.id === userStatus)?.dotColor} mr-1`}
									></span>
									<span class="text-xs text-gray-300">{t(userStatus)}</span>
								</div>
							</div>
						{/if}
					</div>

					<!-- Tooltip for avatar in minimized mode -->
					{#if isMinimized}
						<Tooltip triggeredBy="#avatar-trigger" placement="right" class="z-50">
							<!-- {fullname} ({userStatus.charAt(0).toUpperCase() + userStatus.slice(1)}) -->
							{fullname} ({t(userStatus)})
						</Tooltip>
					{/if}
				</div>

				<!-- Slide-up Menu -->
				{#if isMenuOpen}
					<div
						id="slide-up-menu"
						class={`absolute bottom-full left-0 right-0 max-h-[60vh] w-full overflow-hidden rounded-t-lg bg-white shadow-lg transition-all duration-300 ${menuAnimationState === 'opening' || menuAnimationState === 'open' ? 'slide-up-active' : 'slide-up-closing'}`}
					>
						<!-- User Info Header -->
						<div class="flex items-center border-b p-4">
							<Avatar size="md">{name_avartar}</Avatar>
							<div class="ms-3">
								<p class="text-lg font-medium">{fullname}</p>
								<p class="text-sm text-gray-500">{email}</p>
							</div>
						</div>

						<!-- Status Options -->
						<div class="border-b p-4">
							<p class="mb-2 text-sm text-gray-500">
								<!-- Status -->
								{t('status')}
							</p>
							{#each statusOptions as opt}
								<button
									on:click={() => setStatus(opt.id)}
									class={`mb-2 flex w-full items-center rounded p-2 text-left hover:bg-gray-100 ${userStatus === opt.id ? 'bg-gray-100' : ''}`}
								>
									<span class={`h-3 w-3 rounded-full ${opt.dotColor} mr-3`}></span>
									<span class="text-sm">{t(opt.id)}</span>
								</button>
							{/each}
						</div>
						<!-- <div class="p-4 border-b">
                <p class="text-sm text-gray-500 mb-2">
                    {t('language')}
                </p>
                <LanguageToggle />
            </div> -->
						<!-- Actions -->
						<div class="p-4">
							<button
								on:click={navigateToTasks}
								class="mb-2 flex w-full items-center rounded p-2 text-left hover:bg-gray-100"
							>
								<span class="mr-3 text-gray-800">
									<div
										class="nav-icon h-4 w-4"
										style="--icon-url: {getIconUrl('/navbar/my-tasks.svg')}; --icon-color: #000000"
									></div>
								</span>
								<span>{t('tasks')}</span>
							</button>

							<button
								on:click={logout}
								class="flex w-full items-center rounded p-2 text-left text-red-500 hover:bg-gray-100"
							>
								<span class="mr-3">
									<div
										class="nav-icon h-4 w-4"
										style="--icon-url: {getIconUrl('/navbar/logout.svg')}; --icon-color: #000000"
									></div>
								</span>
								<span>{t('logout')}</span>
							</button>
						</div>
					</div>
				{/if}
			</div>
		{:else}
			<div class="border-t border-gray-700 p-4">
				{#if !isMinimized}
					<Button href="/login" class="w-full cursor-pointer">Login</Button>
				{:else}
					<Button
						id="login-button"
						href="/login"
						class="flex w-full cursor-pointer justify-center p-2"
					>
						<span>🔑</span>
					</Button>
					<Tooltip triggeredBy="#login-button" placement="right" class="z-50">Login</Tooltip>
				{/if}
			</div>
		{/if}
	{:else}
		<!-- Loading state while language preference is being determined -->
		<div class="flex flex-1 items-center justify-center">
			<div class="text-sm text-white opacity-75">
				{#if !isMinimized}
					{t('loading')}
				{:else}
					<div
						class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
					></div>
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}

	@keyframes slideDown {
		from {
			transform: translateY(0);
		}
		to {
			transform: translateY(100%);
		}
	}

	.slide-up-active {
		animation: slideUp 0.3s ease-out forwards;
		transform: translateY(0);
	}

	.slide-up-closing {
		animation: slideDown 0.3s ease-in forwards;
	}

	#slide-up-menu {
		z-index: 1;
	}

	#avatar-trigger {
		position: relative;
		z-index: 2;
	}

	.nav-icon {
		mask-image: var(--icon-url);
		-webkit-mask-image: var(--icon-url);
		mask-repeat: no-repeat;
		-webkit-mask-repeat: no-repeat;
		mask-position: center;
		-webkit-mask-position: center;
		mask-size: contain;
		-webkit-mask-size: contain;
		background-color: var(--icon-color);
	}
</style>

<script lang="ts">
	import { t, language } from '$src/lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Alert, Select } from 'flowbite-svelte';
	import { EditOutline, CheckOutline } from 'flowbite-svelte-icons';
	import countries from 'i18n-iso-countries';
	import en from 'i18n-iso-countries/langs/en.json';
	import th from 'i18n-iso-countries/langs/th.json';

	// Register the locales
	countries.registerLocale(en);
	countries.registerLocale(th);
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { page } from '$app/stores';

	export let customer: any;

	let editForm: HTMLFormElement;
	let editModalOpen = false;
	let selectInstances: any = null;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';

	$: role = $page.data.role;
	$: isAgent = role === 'Agent';

	let formData: any = {};

	// Validation states - only for phone, email, and national_id
	let validationState = {
		phone: 'neutral', // 'neutral', 'valid', 'invalid'
		email: 'neutral',
		national_id: 'neutral'
	};

	// Track if form can be submitted (no invalid fields)
	$: canSubmitForm = !Object.values(validationState).includes('invalid');

	// Validation functions
	function validatePhone(phone: string): boolean {
		if (!phone || phone.trim() === '') return true; // Empty is valid
		const cleanPhone = phone.replace(/\D/g, '');
		return cleanPhone.length === 10;
	}

	function validateEmail(email: string): boolean {
		if (!email || email.trim() === '') return true; // Empty is valid
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email.trim());
	}

	function validateNationalId(nationalId: string): boolean {
		if (!nationalId || nationalId.trim() === '') return true; // Empty is valid
		const cleanId = nationalId.replace(/\D/g, '');
		return cleanId.length === 13;
	}

	// Handle national ID input - only allow numbers
	function handleNationalIdInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		// Remove all non-numeric characters
		const numericValue = value.replace(/\D/g, '');
		formData.national_id = numericValue;
		target.value = numericValue;
	}

	// Handle phone input - only allow numbers
	function handlePhoneInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		// Remove all non-numeric characters
		const numericValue = value.replace(/\D/g, '');
		formData.phone = numericValue;
		target.value = numericValue;
	}

	// Reactive validation
	$: if (formData.phone !== undefined) {
		const phone = formData.phone || '';
		const trimmedPhone = phone.trim();
		
		if (trimmedPhone === '') {
			validationState.phone = 'neutral';
		} else {
			validationState.phone = validatePhone(trimmedPhone) ? 'valid' : 'invalid';
		}
	}

	$: if (formData.email !== undefined) {
		const email = formData.email || '';
		const trimmedEmail = email.trim();
		
		if (trimmedEmail === '') {
			validationState.email = 'neutral';
		} else {
			validationState.email = validateEmail(trimmedEmail) ? 'valid' : 'invalid';
		}
	}

	$: if (formData.national_id !== undefined) {
		const nationalId = formData.national_id || '';
		const trimmedNationalId = nationalId.trim();
		
		if (trimmedNationalId === '') {
			validationState.national_id = 'neutral';
		} else {
			validationState.national_id = validateNationalId(trimmedNationalId) ? 'valid' : 'invalid';
		}
	}

	// Get input class based on validation state
	function getInputClass(state: string): string {
		switch (state) {
			case 'valid':
				return 'border-green-500 focus:border-green-500 focus:ring-green-500';
			case 'invalid':
				return 'border-red-500 focus:border-red-500 focus:ring-red-500';
			default:
				return 'border-gray-300 focus:border-blue-500 focus:ring-blue-500';
		}
	}

	// Get country options based on current language
	$: countryOptions = Object.entries(countries.getNames($language || 'en'))
    .map(([code, localizedName]) => {
        const englishName = countries.getName(code, 'en');
        return { 
            value: englishName,
            name: localizedName,
            code 
        };
    })
    .sort((a, b) => a.name.localeCompare(b.name));

	// Gender options
	const genderOptions = [
		{ value: 1, label: 'not_specified' },
		{ value: 2, label: 'male' },
		{ value: 3, label: 'female' },
		{ value: 4, label: 'other' }
	];

	// Language options
	const languageOptions = [
		{ value: 'th', label: 'Thai' },
		{ value: 'en', label: 'English' },
		{ value: 'zh', label: 'Chinese' },
		{ value: 'jp', label: 'Japanese' }
	];

	// Contact method options
	const contactMethodOptions = [
		{ value: 'EMAIL', label: 'Email' },
		{ value: 'PHONE', label: 'Phone Call' },
		{ value: 'SMS', label: 'SMS' },
		{ value: 'LINE', label: 'LINE' },
		{ value: 'WHATSAPP', label: 'WhatsApp' }
	];

	function maskPhoneNumber(phone: string): string {
		if (!phone) return '';
		if (isAgent) {
			const len = phone.length;
			if (len <= 4) return phone;
			return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
		}
		return phone;
	}

	function openEditModal(customer: any) {
		selectInstances = { ...customer };
		editModalOpen = true;
		
		// Reset messages and validation states
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		validationState = {
			phone: 'neutral',
			email: 'neutral',
			national_id: 'neutral'
		};

		// Initialize formData with existing values
		formData = {
			name: customer.name || '',
			email: customer.email || '',
			date_of_birth: customer.date_of_birth || '',
			phone: isAgent ? maskPhoneNumber(customer.phone || '') : customer.phone || '',
			address_line1: customer.address?.address_line1 || '',
			address_line2: customer.address?.address_line2 || '',
			city: customer.address?.city || '',
			state_province_region: customer.address?.state_province_region || '',
			zip_code: customer.address?.zip_code || '',
			country: customer.address?.country || customer.country || '',
			first_name: customer.first_name || '',
			last_name: customer.last_name || '',
			middle_name: customer.middle_name || '',
			nickname: customer.nickname || '',
			gender_id: customer.gender_id || customer.gender || '',  
			nationality: customer.nationality || '',
			national_id: customer.national_id || '',
			passport_number: customer.passport_number || '',
			career: customer.career || '',
			preferred_language: customer.preferred_language || '',  
			preferred_contact_method: customer.preferred_contact_method || ''  
		};

		console.log('Form data initialized:', formData);
	}

	function handleEditSubmit(event: Event) {
		// Prevent submission if form has invalid fields
		if (!canSubmitForm) {
			event.preventDefault();
			showErrorMessage = true;
			errorMessage = t('please_fix_validation_errors');
			return false;
		}

		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		return true;
	}

	// Get validation message text
	function getValidationMessage(field: string, state: string): string {
		if (state === 'neutral') {
			switch (field) {
				case 'phone': return t('validate_phone_number');
				case 'email': return t('validate_email');
				case 'national_id': return t('validate_national_id');
				default: return t('placeholder_message');
			}
		} else if (state === 'valid') {
			switch (field) {
				case 'phone': return t('validate_phone_number_success');
				case 'email': return t('validate_email_success');
				case 'national_id': return t('validate_national_id_success');
				default: return t('valid');
			}
		} else if (state === 'invalid') {
			switch (field) {
				case 'phone': return t('validate_phone_number');
				case 'email': return t('validate_email');
				case 'national_id': return t('validate_national_id');
				default: return t('invalid');
			}
		}
		return '';
	}

	$: enhanceOptions = {
		modalOpen: editModalOpen,
		setModalOpen: (value: boolean) => (editModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value),
		onSuccess: (result: any) => {
			if (result?.data?.customer) {
				customer = result.data.customer;
				openEditModal(customer);
			} else {
				window.location.reload();
			}
		}
	};
</script>

<style>
	.validation-message {
		font-size: 0.875rem;
		margin-top: 0.25rem;
		display: flex;
		align-items: center;
		gap: 0.25rem;
		min-height: 1.5rem; /* Ensure consistent height for all fields */
	}
	
	.validation-message.valid {
		color: #16a34a;
	}
	
	.validation-message.invalid {
		color: #dc2626;
	}
	
	.validation-message.neutral {
		color: #6b7280;
	}

	.validation-message.empty {
		visibility: hidden; /* Keep space but hide content */
	}

	.validation-icon {
		width: 1rem;
		height: 1rem;
		border-radius: 50%;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		font-size: 0.75rem;
		font-weight: bold;
		flex-shrink: 0;
	}
	
	.validation-icon.valid {
		background-color: #16a34a;
		color: white;
	}
	
	.validation-icon.invalid {
		background-color: #dc2626;
		color: white;
	}
	
	.validation-icon.neutral {
		background-color: #6b7280;
		color: white;
	}

	.field-container {
		margin-bottom: 1rem;
	}

	.submit-button-disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}
</style>

<Button type="button" color="blue" on:click={() => openEditModal(customer)}>
	<EditOutline class="mr-2 h-4 w-4" />
	{t('edit_customer')}
</Button>

<Modal bind:open={editModalOpen} size="xl" title={t('edit_customer')}>
	<svelte:fragment slot="header">
		<h2>{t('edit_customer')}</h2>
	</svelte:fragment>

	{#if selectInstances}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}



		<form
			bind:this={editForm}
			action="?/update_customer"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleEditSubmit}
		>
			<input type="hidden" name="customer_id" value={customer.customer_id} />

			<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
				<!-- First Column: Personal Information -->
				<div class="space-y-1">
					<h3 class="mb-4 border-b pb-2 text-lg font-semibold text-gray-800">
						{t('personal_information')}
					</h3>

					<div class="field-container">
						<Label for="name" class="font-medium">{t('display_name')}</Label>
						<Input
							id="name"
							name="name"
							type="text"
							bind:value={formData.name}
							class="mt-1"
							readonly
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="first_name" class="font-medium">{t('first_name')}</Label>
						<Input
							id="first_name"
							name="first_name"
							type="text"
							bind:value={formData.first_name}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="middle_name" class="font-medium">{t('middle_name')}</Label>
						<Input
							id="middle_name"
							name="middle_name"
							type="text"
							bind:value={formData.middle_name}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="last_name" class="font-medium">{t('last_name')}</Label>
						<Input
							id="last_name"
							name="last_name"
							type="text"
							bind:value={formData.last_name}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="nickname" class="font-medium">{t('nickname')}</Label>
						<Input
							id="nickname"
							name="nickname"
							type="text"
							bind:value={formData.nickname}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="gender_id" class="font-medium">{t('gender')}</Label>
						<Select 
							id="gender_id" 
							name="gender_id" 
							bind:value={formData.gender_id} 
							placeholder={t('select_gender')}
							class="mt-1"
						>
							{#each genderOptions as option}
								<option value={option.value}>{t(option.label)}</option>
							{/each}
						</Select>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<!-- <div class="field-container">
						<Label for="date_of_birth" class="font-medium">{t('date_of_birth')}</Label>
						<Input
							id="date_of_birth"
							name="date_of_birth"
							type="date"
							bind:value={formData.date_of_birth}
							placeholder={t('date_of_birth_placeholder')}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div> -->
					<div class="field-container">
						<Label for="date_of_birth" class="font-medium">{t('date_of_birth')}</Label>
						<Input
							id="date_of_birth"
							name="date_of_birth"
							type="date"
							bind:value={formData.date_of_birth}
							placeholder={t('date_of_birth_placeholder')}
							class="mt-1"
						/>
						<div class="validation-message neutral">
							{t('date_of_birth_system_note')}
						</div>
					</div>
				</div>

				<!-- Second Column: Contact & Identity Information -->
				<div class="space-y-1">
					<h3 class="mb-4 border-b pb-2 text-lg font-semibold text-gray-800">
						{t('contact_identity_information')}
					</h3>

					<div class="field-container">
						<Label for="phone" class="font-medium">{t('phone_number')} </Label>
						<Input 
							id="phone" 
							name="phone" 
							type="text" 
							bind:value={formData.phone} 
							on:input={handlePhoneInput}
							class="mt-1 {getInputClass(validationState.phone)}"
							placeholder="0812345678"
							maxlength="10"
						/>
						<div class="validation-message {validationState.phone}">
							<span class="validation-icon {validationState.phone}">
								{#if validationState.phone === 'valid'}✓{:else if validationState.phone === 'invalid'}✗{:else}#{/if}
							</span>
							{getValidationMessage('phone', validationState.phone)}
						</div>
					</div>

					<div class="field-container">
						<Label for="email" class="font-medium">{t('email')} </Label>
						<Input 
							id="email" 
							name="email" 
							type="email" 
							bind:value={formData.email} 
							class="mt-1 {getInputClass(validationState.email)}"

						/>
						<div class="validation-message {validationState.email}">
							<span class="validation-icon {validationState.email}">
								{#if validationState.email === 'valid'}✓{:else if validationState.email === 'invalid'}✗{:else}@{/if}
							</span>
							{getValidationMessage('email', validationState.email)}
						</div>
					</div>

					<div class="field-container">
						<Label for="nationality" class="font-medium">{t('nationality')}</Label>
						<Select
							id="nationality"
							name="nationality"
							bind:value={formData.nationality}
							placeholder={t('select_nationality')}
							class="mt-1"
						>
							{#each countryOptions as country}
								<option value={country.value}>{country.name}</option>
							{/each}
						</Select>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="national_id" class="font-medium">{t('national_id')} </Label>
						<Input
							id="national_id"
							name="national_id"
							type="text"
							bind:value={formData.national_id}
							on:input={handleNationalIdInput}
							class="mt-1 {getInputClass(validationState.national_id)}"
							maxlength="13"
						/>
						<div class="validation-message {validationState.national_id}">
							<span class="validation-icon {validationState.national_id}">
								{#if validationState.national_id === 'valid'}✓{:else if validationState.national_id === 'invalid'}✗{:else}#{/if}
							</span>
							{getValidationMessage('national_id', validationState.national_id)}
						</div>
					</div>

					<div class="field-container">
						<Label for="passport_number" class="font-medium">{t('passport_number')}</Label>
						<Input
							id="passport_number"
							name="passport_number"
							type="text"
							bind:value={formData.passport_number}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="career" class="font-medium">{t('career')}</Label>
						<Input
							id="career"
							name="career"
							type="text"
							bind:value={formData.career}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="preferred_language" class="font-medium">{t('preferred_language')}</Label>
						<Select
							id="preferred_language"
							name="preferred_language"
							bind:value={formData.preferred_language}
							placeholder={t('select_language')}
							class="mt-1"
						>
							{#each languageOptions as lang}
								<option value={lang.value}>{lang.label}</option>
							{/each}
						</Select>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="preferred_contact_method" class="font-medium"
							>{t('preferred_contact_method')}</Label
						>
						<Select
							id="preferred_contact_method"
							name="preferred_contact_method"
							bind:value={formData.preferred_contact_method}
							placeholder={t('select_contact_method')}
							class="mt-1"
						>
							{#each contactMethodOptions as method}
								<option value={method.value}>{method.label}</option>
							{/each}
						</Select>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>
				</div>

				<!-- Third Column: Address Information -->
				<div class="space-y-1">
					<h3 class="mb-4 border-b pb-2 text-lg font-semibold text-gray-800">
						{t('address_information')}
					</h3>

					<div class="field-container">
						<Label for="address_line1" class="font-medium">{t('address_line1')}</Label>
						<Input
							id="address_line1"
							name="address_line1"
							type="text"
							bind:value={formData.address_line1}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="address_line2" class="font-medium">{t('address_line2')}</Label>
						<Input
							id="address_line2"
							name="address_line2"
							type="text"
							bind:value={formData.address_line2}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="city" class="font-medium">{t('city')}</Label>
						<Input id="city" name="city" type="text" bind:value={formData.city} class="mt-1" />
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="state_province_region" class="font-medium"
							>{t('state_province_region')}</Label
						>
						<Input
							id="state_province_region"
							name="state_province_region"
							type="text"
							bind:value={formData.state_province_region}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="zip_code" class="font-medium">{t('zip_code')}</Label>
						<Input
							id="zip_code"
							name="zip_code"
							type="text"
							bind:value={formData.zip_code}
							class="mt-1"
						/>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>

					<div class="field-container">
						<Label for="country" class="font-medium">{t('country')}</Label>
						<Select 
							id="country" 
							name="country"
							bind:value={formData.country} 
							placeholder={t('select_country')}
							class="mt-1"
						>
							{#each countryOptions as country}
								<option value={country.value}>{country.name}</option>
							{/each}
						</Select>
						<div class="validation-message empty">
							<span class="validation-icon neutral">-</span>
							{t('placeholder_message')}
						</div>
					</div>
				</div>
			</div>
		</form>
	{/if}

	<svelte:fragment slot="footer">
		<Button 
			type="submit" 
			color={canSubmitForm ? "green" : "light"}
			disabled={!canSubmitForm}
			class={!canSubmitForm ? "submit-button-disabled" : ""}
			on:click={() => editForm.requestSubmit()}
		>
			<CheckOutline class="mr-2 h-4 w-4" />
			{t('update')}
		</Button>
		<Button color="light" on:click={() => (editModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { cubicOut } from "svelte/easing";
import type { TransitionConfig } from "svelte/transition";
import { t, language } from '$lib/stores/i18n';
import { get } from 'svelte/store';
import type { Message } from '$lib/types/customer';

// Import i18n-iso-countries
import countries from 'i18n-iso-countries';

// Register the locales you need
import enLocale from 'i18n-iso-countries/langs/en.json';
import thLocale from 'i18n-iso-countries/langs/th.json';

// Register locales
countries.registerLocale(enLocale);
countries.registerLocale(thLocale);

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

type FlyAndScaleParams = {
	y?: number;
	x?: number;
	start?: number;
	duration?: number;
};

export const flyAndScale = (
	node: Element,
	params: FlyAndScaleParams = { y: -8, x: 0, start: 0.95, duration: 150 }
): TransitionConfig => {
	const style = getComputedStyle(node);
	const transform = style.transform === "none" ? "" : style.transform;

	const scaleConversion = (
		valueA: number,
		scaleA: [number, number],
		scaleB: [number, number]
	) => {
		const [minA, maxA] = scaleA;
		const [minB, maxB] = scaleB;

		const percentage = (valueA - minA) / (maxA - minA);
		const valueB = percentage * (maxB - minB) + minB;

		return valueB;
	};

	const styleToString = (
		style: Record<string, number | string | undefined>
	): string => {
		return Object.keys(style).reduce((str, key) => {
			if (style[key] === undefined) return str;
			return str + `${key}:${style[key]};`;
		}, "");
	};

	return {
		duration: params.duration ?? 200,
		delay: 0,
		css: (t) => {
			const y = scaleConversion(t, [0, 1], [params.y ?? 5, 0]);
			const x = scaleConversion(t, [0, 1], [params.x ?? 0, 0]);
			const scale = scaleConversion(t, [0, 1], [params.start ?? 0.95, 1]);

			return styleToString({
				transform: `${transform} translate3d(${x}px, ${y}px, 0) scale(${scale})`,
				opacity: t
			});
		},
		easing: cubicOut
	};
};

// Format timestamp to local time
// export function formatTimestamp(timestamp: string): string {
//     return new Date(timestamp).toLocaleTimeString([], { 
//         year: 'numeric',
//         month: 'long',
//         day: '2-digit',
//         hour: '2-digit', 
//         minute: '2-digit',
//         second: '2-digit',
//     });
// };

export function formatTimestamp(timestamp: string) {
    const date = new Date(timestamp);
    const lang = get(language)
    const options = {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    };

    const locale = lang === 'th' ? 'th-TH' : 'en-US';

    return date.toLocaleString(locale, options).replace(',', '');
}


export function formatTimestampDMY(timestamp: string) {
    const date = new Date(timestamp);
    const lang = get(language)
    const options = {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
    };

    const locale = lang === 'th' ? 'th-TH' : 'en-US';

    return date.toLocaleString(locale, options).replace(',', '');
}

// export function displayDate(timestamp: string): string {
//     const d = new Date(timestamp);
//     const day   = d.getDate().toString().padStart(2, '0');
//     const month = d.toLocaleString('en-US', { month: 'short' });
//     const year  = d.getFullYear();
//     const hour  = d.getHours().toString().padStart(2, '0');
//     const minute = d.getMinutes().toString().padStart(2, '0');
    
//     // return `${day} ${month} ${year} ${hour}:${minute}`;
//     return {
//         date: `${day} ${month} ${year}`,
//         time: `${hour}:${minute}`
//     };
// }

export function displayDate(timestamp: string): { date: string; time: string } {
	if (!timestamp) {
		return { date: '-', time: '' };
	}

	const d = new Date(timestamp);
	const lang = get(language); // 'en' or 'th'

	const day = d.getDate().toString().padStart(2, '0');
	const month = d.toLocaleString(lang === 'th' ? 'th-TH' : 'en-US', { month: 'short' });

	// Adjust year for Thai Buddhist Era if language is 'th'
	const year = lang === 'th' ? d.getFullYear() + 543 : d.getFullYear();

	const hour = d.getHours().toString().padStart(2, '0');
	const minute = d.getMinutes().toString().padStart(2, '0');

	return {
		date: `${day} ${month} ${year}`,
		time: `${hour}:${minute}`
	};
}


// export function timeAgo(timestamp: string, ticket_status: string): string {
//     const now = new Date();
//     const lastActiveTime = new Date(timestamp);
//     const diffInSeconds = Math.floor((now.getTime() - lastActiveTime.getTime()) / 1000);
//     const diffInMinutes = Math.floor(diffInSeconds / 60);
//     const diffInHours = Math.floor(diffInMinutes / 60);
//     const diffInDays = Math.floor(diffInHours / 24);
//     const diffInWeeks = Math.floor(diffInDays / 7);
//     const diffInMonths = Math.floor(diffInDays / 30); // Approximate
//     const diffInYears = Math.floor(diffInDays / 365); // Approximate

//     // if (ticket_status === 'close') {
//     // 	return 'Closed';
//     // }

//     if (diffInYears > 0) {
//         return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
//     } else if (diffInMonths > 0) {
//         return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
//     } else if (diffInWeeks > 0) {
//         return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
//     } else if (diffInDays > 0) {
//         return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
//     } else if (diffInHours > 0) {
//         return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
//     } else if (diffInMinutes > 0) {
//         return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
//     } else {
//         return 'Just now';
//     }
// }

export function timeAgo(timestamp: string, ticket_status: string): string {
    const now = new Date();
    const lastActiveTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - lastActiveTime.getTime()) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInWeeks = Math.floor(diffInDays / 7);
    const diffInMonths = Math.floor(diffInDays / 30); // Approximate
    const diffInYears = Math.floor(diffInDays / 365); // Approximate

    const format = (num: number, unitKey: string, pluralKey: string) =>
        `${num} ${t(num === 1 ? unitKey : pluralKey)} ${t('time_ago.ago')}`;

    if (diffInYears > 0) return format(diffInYears, 'time_ago.year', 'time_ago.years');
    if (diffInMonths > 0) return format(diffInMonths, 'time_ago.month', 'time_ago.months');
    if (diffInWeeks > 0) return format(diffInWeeks, 'time_ago.week', 'time_ago.weeks');
    if (diffInDays > 0) return format(diffInDays, 'time_ago.day', 'time_ago.days');
    if (diffInHours > 0) return format(diffInHours, 'time_ago.hour', 'time_ago.hours');
    if (diffInMinutes > 0) return format(diffInMinutes, 'time_ago.minute', 'time_ago.minutes');
    return t('time_ago.just_now');
}



export function getStatusClass(statusId: number): string {
    const statusClasses = {
      2: 'flex item-center justify-center rounded-md bg-green-200 p-2 text-sm text-green-700', // open
      3: 'flex item-center justify-center rounded-md bg-blue-200 p-2 text-sm text-blue-700',   // assigned
      4: 'flex item-center justify-center rounded-md bg-yellow-200 p-2 text-sm text-yellow-700', // waiting
      5: 'flex item-center justify-center rounded-md bg-gray-100 p-2 text-sm text-gray-700',   // pending to close
      6: 'flex item-center justify-center rounded-md bg-gray-100 p-2 text-sm text-gray-700',   // closed
    };
    
    return statusClasses[statusId] || 'flex item-center justify-center rounded-md bg-gray-100 p-2 text-sm text-gray-700';
}

export function getPriorityClass(priorityName: string): string {
    const priorityClasses = {
      'Low': 'flex items-center justify-center rounded-md p-2 bg-gray-100 text-gray-700',
      'Medium': 'flex items-center justify-center rounded-md p-2 bg-yellow-200 text-yellow-700',
      'High': 'flex items-center justify-center rounded-md p-2 bg-orange-200 text-orange-700',
      'Immediately': 'flex items-center justify-center rounded-md p-2 bg-red-200 text-red-700',
    };
    
    return priorityClasses[priorityName] || 'flex items-center justify-center rounded-md p-2 bg-gray-100 text-gray-700';
}
  
export function getSentimentClass(sentiment: string | undefined): string {
    const sentimentClasses = {
      'Positive': 'bg-green-200 text-green-700',
      'Neutral': 'bg-gray-100 text-gray-700',
      'Negative': 'bg-red-200 text-red-700',
    };
    
    return sentimentClasses[sentiment || ''] || 'bg-gray-100 text-gray-700';
}   
  
export function getSentimentIcon(sentiment: string | undefined): string {
    const sentimentIcons = {
      'Positive': '/images/sentiment-positive.png',
      'Neutral': '/images/sentiment-neutral.png',
      'Negative': '/images/sentiment-negative.png',
    };
    
    return sentimentIcons[sentiment || ''] || '/images/sentiment-non.png';
}

export const colorOptions = [
    { name: "red", class: "bg-red-500" },
    { name: "orange", class: "bg-orange-500" },
    { name: "amber", class: "bg-amber-500" },
    { name: "yellow", class: "bg-yellow-500" },
    { name: "pink", class: "bg-pink-500" },
    { name: "purple", class: "bg-purple-400" },
    { name: "violet", class: "bg-violet-500" },
    { name: "blue", class: "bg-blue-500" },
    { name: "cyan", class: "bg-cyan-500" },
    { name: "green", class: "bg-green-500" },
    { name: "lime", class: "bg-lime-500" },
    { name: "gray", class: "bg-gray-500" }
];

export function getColorClass(selectedColor: string): string {
    return colorOptions.find(c => c.name === selectedColor)?.class || 'bg-gray-500';
}

// Enhanced badge configuration functions
export function getStatusBadgeConfig(id: number, status: string) {
    const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
        none: {
            class: '',
            text: '',
            showIcon: false
        },
        closed: {
            class: getStatusClass(id),
            text: t('tickets_closed'),
            showIcon: true
        },
        open: {
            class: getStatusClass(id),
            text: t('tickets_open'),
            showIcon: false
        },
        assigned: {
            class: getStatusClass(id),
            text: t('tickets_assigned'),
            showIcon: false
        },
        waiting: {
            class: getStatusClass(id),
            text: t('tickets_waiting'),
            showIcon: false
        },
        pending_to_close: {
            class: getStatusClass(id),
            text: t('tickets_pending_to_close'),
            showIcon: false
        }
    };
    return configs[status?.toLowerCase()] || configs['none'];
}

export function getPriorityBadgeConfig(priorityName: string) {
    const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
        none: {
            class: '',
            text: '',
            showIcon: false
        },
        Low: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_low'),
            showIcon: false
        },
        Medium: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_medium'),
            showIcon: true
        },
        High: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_high'),
            showIcon: true
        },
        Immediately: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_immediately'),
            showIcon: true
        }
    };
    return configs[priorityName] || configs['none'];
}

// Group messages by date
export function groupMessagesByDate(messages: Message[]) {
    const groups: { date: string; messages: Message[] }[] = [];
    let currentDate = '';
    
    messages.forEach(msg => {
        const msgDate = new Date(msg.created_on).toLocaleDateString();
        
        if (msgDate !== currentDate) {
            currentDate = msgDate;
            groups.push({
                date: msgDate,
                messages: [msg]
            });
        } else {
            groups[groups.length - 1].messages.push(msg);
        }
    });
    
    return groups;
}

// Check if should show avatar (first message or different sender)
export function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
    if (index === 0) return true;
    const prevMessage = messages[index - 1];
    return prevMessage.is_self !== message.is_self || 
            prevMessage.user_name !== message.user_name;
}


// 1. Date of Birth Formatting Function
export function formatDateOfBirth(dateString, locale = 'en') {
    if (!dateString) return '';
    
    try {
        const date = new Date(dateString);
        
        // Check if date is valid
        if (isNaN(date.getTime())) return dateString;
        
        const options = {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        };
        
        // Format based on locale
        if (locale === 'th') {
            // Thai locale with Buddhist calendar and era
            const thaiOptions = {
                ...options,
                calendar: 'buddhist',
                era: 'short' // This ensures พ.ศ. is included
            };
            const formatted = date.toLocaleDateString('th-TH', thaiOptions);
            
            // Fallback: If era is not included, manually add พ.ศ.
            if (!formatted.includes('พ.ศ.')) {
                const buddhistYear = date.getFullYear() + 543;
                const dayMonth = date.toLocaleDateString('th-TH', {
                    day: 'numeric',
                    month: 'long'
                });
                return `${dayMonth} พ.ศ. ${buddhistYear}`;
            }
            
            return formatted;
        } else {
            // English locale
            return date.toLocaleDateString('en-US', options);
        }
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateString;
    }
}

// Helper function to convert country name to nationality (English only)
function countryToNationality(countryName: string): string {
    // Handle special cases that don't follow simple patterns
    const specialCases: Record<string, string> = {
        'United States': 'American',
        'United Kingdom': 'British',
        'Netherlands': 'Dutch',
        'Switzerland': 'Swiss',
        'New Zealand': 'New Zealander',
        'South Africa': 'South African',
        'Saudi Arabia': 'Saudi Arabian',
        'United Arab Emirates': 'Emirati',
        'Czech Republic': 'Czech',
        'South Korea': 'South Korean'
    };
    
    // Check special cases first
    if (specialCases[countryName]) {
        return specialCases[countryName];
    }
    
    // Apply common patterns for regular countries
    if (countryName.endsWith('land')) {
        // Finland -> Finnish, Thailand -> Thai, etc.
        if (countryName === 'Thailand') return 'Thai';
        if (countryName === 'Finland') return 'Finnish';
        if (countryName === 'Poland') return 'Polish';
        return countryName.replace('land', '') + 'ish';
    }
    
    if (countryName.endsWith('way')) {
        // Norway -> Norwegian
        return countryName.replace('way', 'wegian');
    }
    
    if (countryName.endsWith('mark')) {
        // Denmark -> Danish
        return countryName.replace('mark', 'nish');
    }
    
    if (countryName.endsWith('den')) {
        // Sweden -> Swedish
        return countryName.replace('den', 'dish');
    }
    
    if (countryName.endsWith('ia')) {
        // Australia -> Australian, Romania -> Romanian
        return countryName + 'n';
    }
    
    if (countryName.endsWith('a')) {
        // China -> Chinese, India -> Indian
        if (countryName === 'China') return 'Chinese';
        return countryName + 'n';
    }
    
    if (countryName.endsWith('y')) {
        // Germany -> German, Italy -> Italian
        if (countryName === 'Germany') return 'German';
        if (countryName === 'Italy') return 'Italian';
        if (countryName === 'Turkey') return 'Turkish';
        if (countryName === 'Hungary') return 'Hungarian';
        return countryName.replace('y', 'ian');
    }
    
    if (countryName.endsWith('ce')) {
        // France -> French, Greece -> Greek
        if (countryName === 'France') return 'French';
        if (countryName === 'Greece') return 'Greek';
        return countryName.replace('ce', 'ch');
    }
    
    if (countryName.endsWith('an')) {
        // Japan -> Japanese
        if (countryName === 'Japan') return 'Japanese';
        return countryName + 'ese';
    }
    
    if (countryName.endsWith('l')) {
        // Brazil -> Brazilian, Portugal -> Portuguese
        if (countryName === 'Brazil') return 'Brazilian';
        if (countryName === 'Portugal') return 'Portuguese';
        return countryName + 'ian';
    }
    
    // Default: just add 'ian' for most other countries
    return countryName + 'ian';
}


// 2. Enhanced Nationality Function using i18n-iso-countries
export function getFullNationality(nationalityCode: string, locale: string = 'en'): string {
    if (!nationalityCode) return '';
    
    try {
        const nationalityValue = nationalityCode.toString().trim();
        
        // Check if it's a 2-letter country code
        if (nationalityValue.length === 2) {
            // It's a country code, convert to full name then to nationality
            const code = nationalityValue.toUpperCase();
            const countryName = countries.getName(code, locale);
            
            if (countryName) {
                if (locale === 'en') {
                    // Convert country name to nationality for English
                    return countryToNationality(countryName);
                } else {
                    // For Thai and other locales, return the country name
                    return countryName;
                }
            }
            
            return nationalityValue;
        } else {
            // It's already a full country name
            if (locale === 'en') {
                // If it's English and already a full name, convert to nationality
                return countryToNationality(nationalityValue);
            } else {
                // For other locales, try to get the localized country name
                // First, find the country code from the English name
                const englishNames = countries.getNames('en');
                const countryCode = Object.entries(englishNames)
                    .find(([code, name]) => name.toLowerCase() === nationalityValue.toLowerCase())?.[0];
                
                if (countryCode) {
                    // Get the localized country name
                    const localizedCountryName = countries.getName(countryCode, locale);
                    return localizedCountryName || nationalityValue;
                }
                
                // If no code found, use the original full name
                return nationalityValue;
            }
        }
    } catch (error) {
        console.error('Error getting nationality:', error);
        return nationalityCode;
    }
}

// 3. Enhanced Address Formatting Function using i18n-iso-countries
export function formatFullAddress(address: any, locale: string = 'en'): string {
    if (!address) return '';
    
    const addressParts: string[] = [];
    
    // Add address components in order
    if (address.address_line1) {
        addressParts.push(address.address_line1.trim());
    }
    
    if (address.address_line2) {
        addressParts.push(address.address_line2.trim());
    }
    
    // Handle district/subdistrict
    if (address.district) {
        addressParts.push(address.district.trim());
    }
    
    if (address.sub_district) {
        addressParts.push(address.sub_district.trim());
    }
    
    // Handle city
    if (address.city) {
        addressParts.push(address.city.trim());
    }
    
    // Handle province/state
    if (address.province) {
        addressParts.push(address.province.trim());
    } else if (address.state_province_region) {
        addressParts.push(address.state_province_region.trim());
    }
    
    // Handle postal/zip code
    if (address.postal_code) {
        addressParts.push(address.postal_code.trim());
    } else if (address.zip_code) {
        addressParts.push(address.zip_code.trim());
    }
    
    // Handle country - now supports both codes and full names
    if (address.country) {
        try {
            const countryValue = address.country.toString().trim();
            let displayCountryName = countryValue;
            
            // Check if it's a 2-letter country code
            if (countryValue.length === 2) {
                // It's a country code, convert to full name in desired locale
                displayCountryName = countries.getName(countryValue.toUpperCase(), locale) || countryValue;
            } else {
                // It's already a full country name
                if (locale !== 'en') {
                    // If we want a different locale, try to get the country code first
                    // then get the localized name
                    const englishNames = countries.getNames('en');
                    const countryCode = Object.entries(englishNames)
                        .find(([code, name]) => name.toLowerCase() === countryValue.toLowerCase())?.[0];
                    
                    if (countryCode) {
                        displayCountryName = countries.getName(countryCode, locale) || countryValue;
                    }
                    // If no code found, use the original full name
                }
                // If locale is 'en' or we couldn't find the code, use the stored full name as is
            }
            
            addressParts.push(displayCountryName);
        } catch (error) {
            console.error('Error processing country name:', error);
            addressParts.push(address.country.trim());
        }
    }
    
    // Join with appropriate separator based on locale
    const separator = locale === 'th' ? ' ' : ', ';
    return addressParts.filter(part => part && part !== '').join(separator);
}

// Helper function to check if address has any meaningful data
export function hasValidAddress(address: any): boolean {
    if (!address) return false;
    
    const addressFields = [
        'address_line1',
        'address_line2', 
        'city',
        'district',
        'sub_district',
        'province',
        'state_province_region',
        'zip_code',
        'postal_code',
        'country'
    ];
    
    return addressFields.some(field => 
        address[field] && address[field].toString().trim() !== ''
    );
}
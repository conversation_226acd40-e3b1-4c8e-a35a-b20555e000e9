<script lang="ts">
	// import * as Form from '$lib/components/ui/form';
	// import { Input } from '$lib/components/ui/input';
	// import * as Alert from '$lib/components/ui/alert';

	import { formSchema, type FormSchema, type Response } from './schema';
	import SuperDebug, { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { Button, Label, Input, Card, Toggle } from 'flowbite-svelte';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import { Field, Control, FieldErrors } from 'formsnap';
	import { <PERSON><PERSON>, Spinner } from 'flowbite-svelte';
	import { EyeOutline, EyeSlashOutline, UserOutline, LockOutline } from 'flowbite-svelte-icons';

	export let data: SuperValidated<Infer<FormSchema>>;
	export let response: Response;

	const form = superForm(data, {
		validators: zodClient(formSchema)
	});

	const { form: formData, enhance, delayed } = form;

	// Client-side validation state for username
	let usernameValidationError = '';
	let usernameBlurred = false;

	// Password visibility toggle state
	let showPassword = false;

	// Username input handler with filtering and validation
	function handleUsernameInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// Filter out non-alphanumeric characters, allowing dots, underscores, and hyphens. Do not allow multiple hyphens e.g., '--'
		const filteredValue = target.value.replace(/[^a-zA-Z0-9._-]/g, '').replace(/--/g, '-');

		// Update form data with filtered value
		$formData.username = filteredValue;

		// Clear validation error when user types valid characters
		if (filteredValue.length > 0) {
			usernameValidationError = '';
		} else if (usernameBlurred) {
			// Show required error if field is empty and was blurred
			usernameValidationError = 'Username is required';
		}

		// Show error if invalid characters were filtered out
		if (filteredValue !== target.value && filteredValue.length > 0) {
			usernameValidationError =
				'Username must contain only letters and numbers, dots, underscores, or hyphens';
		}

		// Update the input value to reflect the filtered value
		target.value = filteredValue;
	}

	// Handle username blur event
	function handleUsernameBlur(event: Event) {
		const target = event.target as HTMLInputElement;
		usernameBlurred = true;

		if (target.value.trim() === '') {
			usernameValidationError = 'Username is required';
		}
	}

	// Handle paste events to filter pasted content
	function handleUsernamePaste(event: ClipboardEvent) {
		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';

		// Filter out non-alphanumeric characters, allowing dots, underscores, and hyphens. Do not allow multiple hyphens e.g., '--'
		const filteredText = pastedText.replace(/[^a-zA-Z0-9._-]/g, '').replace(/--/g, '-');

		// Update form data with filtered value
		$formData.username = filteredText;

		// Clear validation errors if we have valid content
		if (filteredText.length > 0) {
			usernameValidationError = '';
		}

		// Show validation message if content was filtered
		if (filteredText !== pastedText && filteredText.length > 0) {
			usernameValidationError =
				'Username must contain only letters and numbers, dots, underscores, or hyphens';
		}

		// Update the input element directly
		const target = event.target as HTMLInputElement;
		target.value = filteredText;

		// Trigger input event to ensure reactivity
		target.dispatchEvent(new Event('input', { bubbles: true }));
	}

	// Toggle password visibility
	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}
</script>

<!-- <SuperDebug data={response} /> -->
<!-- Main Container -->
<!-- <br /> -->

<div class="row text-center">
	<!-- Left Side - Image -->
	<!-- <div class="column">
        <img src="/images/Salmate-Logo-Transparent.png" alt="Salmate Logo" class="w-64 h-64 object-contain">
    </div> -->

	<!-- Right Side - Login -->
	<div class="column">
		<form class="flex flex-col" action="?/login" method="POST" use:enhance>
			<h3 class="dark:text-white mb-5 text-left text-5xl font-bold text-gray-700">Salmate</h3>
			<br />

			<!-- Authentication Error Alert -->
			{#if response?.form?.message?.status === 'fail'}
				<Alert color="red" class="animate-in slide-in-from-top-3 mb-4 duration-300 ease-in-out">
					{response?.form?.message?.detail || 'Invalid username or password. Please try again.'}
				</Alert>
			{/if}

			<Field {form} name="username">
				<Control let:attrs>
					<Label class="text-left" for="username">Username</Label>
					<div class="relative mb-2">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<UserOutline class="h-5 w-5 text-gray-500" />
						</div>
						<Input
							{...attrs}
							id="username"
							name="username"
							type="text"
							bind:value={$formData.username}
							placeholder="Enter your username"
							class="focus:ring-blue ps-10 transition-all duration-300 ease-in-out focus:border-transparent focus:ring-2 {usernameValidationError
								? 'border-red-500 focus:ring-red-500'
								: ''}"
							on:input={handleUsernameInput}
							on:blur={handleUsernameBlur}
							on:paste={handleUsernamePaste}
							maxlength="20"
							autofocus
						/>
						<!-- <div class="absolute -bottom-4 left-0 text-xs text-red-500"> -->
						<div
							class="absolute -bottom-8 left-0 text-xs text-red-500 transition-all duration-300 ease-in-out"
						>
							{#if usernameValidationError}
								<span class="animate-in slide-in-from-top-2 duration-300"
									>{usernameValidationError}</span
								>
							{:else}
								<div class="animate-in slide-in-from-top-2 duration-300">
									<FieldErrors />
								</div>
							{/if}
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<Field {form} name="password">
				<Control let:attrs>
					<Label class="text-left" for="password">Password</Label>
					<div class="relative mb-6">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<LockOutline class="h-5 w-5 text-gray-500" />
						</div>
						<Input
							{...attrs}
							id="password"
							name="password"
							type={showPassword ? 'text' : 'password'}
							bind:value={$formData.password}
							placeholder="Enter your password"
							class="focus:ring-blue pe-10 ps-10 transition-all duration-300 ease-in-out focus:border-transparent focus:ring-2"
						/>
						<button
							type="button"
							class="absolute inset-y-0 end-0 flex cursor-pointer items-center pe-3.5 transition-all duration-200 ease-in-out hover:scale-110 hover:text-blue-500 active:scale-95"
							on:click={togglePasswordVisibility}
							aria-label={showPassword ? 'Hide password' : 'Show password'}
						>
							{#if showPassword}
								<EyeSlashOutline
									class="h-5 w-5 text-gray-500 transition-colors duration-200 hover:text-blue-500"
								/>
							{:else}
								<EyeOutline
									class="h-5 w-5 text-gray-500 transition-colors duration-200 hover:text-blue-500"
								/>
							{/if}
						</button>
						<div
							class="absolute -bottom-4 left-0 text-xs text-red-500 transition-all duration-300 ease-in-out"
						>
							<div class="animate-in slide-in-from-top-2 duration-300">
								<FieldErrors />
							</div>
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<div class="flex flex-col gap-0">
				<Button
					type="submit"
					class="w-full transform bg-gradient-to-r from-cyan-400 to-sky-500 transition-all duration-300 ease-in-out hover:scale-[1.02] hover:from-cyan-500 hover:to-sky-600 active:scale-[0.98]"
				>
					{#if $delayed}
						<div class="animate-in fade-in-0 flex items-center duration-300">
							<Spinner class="me-3" size="4" color="white" data-testid="loading-spinner" />
							<span class="animate-pulse">Logging In</span>
						</div>
					{:else}
						<span class="transition-opacity duration-200">Login</span>
					{/if}
				</Button>
			</div>
		</form>
	</div>
</div>

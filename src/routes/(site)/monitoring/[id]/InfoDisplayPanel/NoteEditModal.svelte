<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Textarea } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	export let editModal: boolean = false;
	export let closeModal: () => void;
	export let editNote: { content: string } | null = null;
	export let customerId: string | number | null = null;

	let editNoteForm: HTMLFormElement;

	export let onSuccess: () => void = () => {};

	function handleUpdateButtonClick() {
		editNoteForm.requestSubmit();
	}
</script>

<Modal title={t('note_edit')} bind:open={editModal} autoclose size="sm">
	<form
		bind:this={editNoteForm}
		method="POST"
		enctype="multipart/form-data"
		action="?/update_note"
		use:enhance={() => {
			return async ({ update, result }) => {
				if (result.type === 'success') {
					await update();
					onSuccess();
					// showSuccess = true;
				} else if (result.type === 'failure') {
					// showError = false;
				}
			};
		}}
	>
		<Textarea id="textarea-id" bind:value={editNote.content} rows="3" name="note" type="text" />
		<input type="hidden" name="customerId" value={customerId} />
		<input type="hidden" name="customerNoteId" value={editNote.id} />

		<div class="flex w-full justify-center gap-2">
			<Button color="green" on:click={handleUpdateButtonClick}
				><CheckOutline class="mr-2 h-4 w-4" />{t('save')}</Button
			>
			<Button color="light" on:click={closeModal}>{t('cancel')}</Button>
		</div>
	</form>
</Modal>

<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import { Breadcrumb, BreadcrumbItem, Tabs, TabItem, Button } from 'flowbite-svelte';

	import { DownloadOutline } from 'flowbite-svelte-icons';

	import { getBackendUrl } from '$src/lib/config';
	import type { PageData } from './$types';
	import { enhance } from '$app/forms';
	import { getColorClass } from '$lib/utils';
	import type { CustomerInterface } from '$src/lib/api/types/customer';

	import Memory from '$src/lib/components/customer/detail/memory.svelte';
	import Notes from '$src/lib/components/customer/detail/notes.svelte';
	import Policy from '$src/lib/components/customer/detail/policy.svelte';
	import Ticket from '$src/lib/components/customer/detail/ticket.svelte';
	import Profile from '$src/lib/components/customer/detail/profile.svelte';

	export let data: PageData;
	$: ({
		customer,
		customer_notes,
		customer_policies,
		customer_tickets,
		customer_tags,
		customer_memory,
		access_token
	} = data);

	function exportCustomerChat() {
		const url = `${getBackendUrl()}/customer/api/customers/${customer.customer_id}/messages/`;
		console.log(url);
		const bodyData = { format: 'zip' };

		fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json' // Ensure JSON request
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
			});
	}

    function exportCustomerNotes() {
		const url = `${getBackendUrl()}/customer/api/customers/${customer.customer_id}/notes/`;
		console.log(url);
		const bodyData = { format: 'zip' };

		fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json' // Ensure JSON request
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
			});
	}
</script>

<svelte:head>
	<title>{t('customer_details')}</title>
</svelte:head>

<div class="min-h-screen rounded-lg bg-white">
	<div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
		<!--Breadcrumb -->
		<div>
			<Breadcrumb aria-label="Default breadcrumb example">
				<BreadcrumbItem href="/" home>
					<span class="text-gray-400">{t('home')}</span>
				</BreadcrumbItem>
				<BreadcrumbItem href="/customer">
					<span class="text-gray-400">{t('customers')}</span>
				</BreadcrumbItem>
				<BreadcrumbItem>
					<span class="text-gray-700">{t('detail')}</span>
				</BreadcrumbItem>
			</Breadcrumb>
		</div>

		<!--Customer Informations and Notes -->
		<div class="mt-4 grid grid-cols-1 gap-5 lg:grid-cols-10">
			<!-- Left side: Policy and Ticket Info -->
			<div class="rounded-lg border bg-white p-4 shadow-md lg:col-span-7">
				<Tabs tabStyle="underline">
					<TabItem open title={t('tickets')}>
						<Ticket {customer_tickets} />
					</TabItem>

					<TabItem title={t('policies')}>
						<Policy {customer_policies} />
					</TabItem>

					<TabItem title={t('notes')}>
						<Notes {access_token} {customer} {customer_notes} />
					</TabItem>

					<TabItem title={t('memories')}>
						<Memory memories={customer_memory} />
					</TabItem>
				</Tabs>
			</div>

			<!-- Right side: Customer Profile and Notes -->
			<div class="space-y-4 lg:col-span-3">
				<Profile {customer} {customer_tags} />

				<div class="flex flex-col space-y-3">
					<Button type="button" color="blue" on:click={exportCustomerChat}>
						<DownloadOutline class="mr-2 h-4 w-4" />
						{t('export_customer_conversations')}
					</Button>
					<Button type="button" color="blue" disabled>
						<DownloadOutline class="mr-2 h-4 w-4" />
						{t('export_customer_notes')}</Button
					>
				</div>
			</div>
		</div>
	</div>
</div>
